"""
Kasper-Q Queue Task Manager

This module manages Queue-IT processing tasks for the Kasper-Q system. It coordinates
concurrent queue bypass operations, monitors task progress, and provides real-time
status updates through WebSocket communication.

Key Features:
- Concurrent queue bypass task execution with configurable task counts
- Real-time progress monitoring and status aggregation
- Queue-IT processing stages: Initializing, In Queue, Processing, Generating, Complete, Failed
- Discord webhook notifications for pass link generation and completion
- Thread-safe task management with proper cleanup and error handling
- Integration with WHOP API license validation
- WebSocket broadcasting for real-time frontend updates

The task manager implements a singleton pattern to ensure consistent state management
across the application and provides comprehensive error handling for robust operation.

Processing Stages:
- Initializing: Setting up tasks and preparing for queue entry
- Solving: Actively working on queue bypass challenges
- Queued: Waiting in the queue system
- Waiting: Waiting for queue progression
- Active: Actively processing in the queue
- Completed: Successfully obtained pass links
- Error: Failed due to errors or authentication issues

Author: Kasper-Q Development Team
Created: 2024
Version: 1.0

Example:
    >>> manager = QueueTaskManager()
    >>> manager.set_settings_manager(settings_manager)
    >>> manager.set_websocket_server(websocket_server)
    >>> result = await manager.start_task({
    ...     'task_id': 'task_123',
    ...     'ticket_count': 5,
    ...     'url': 'https://queue.example.com',
    ...     'client_id': 'client_abc'
    ... })
"""

import asyncio
import time
import gc
import weakref
from threading import Thread
from typing import Dict, Any, List, Optional, Union, Tuple
import aiohttp
import ctypes
import threading
import psutil

from queue_it.async_main import AsyncQueueItHandler  # Async handler for better performance
from tasks_logger import Logger
from helpers import settings_manager
from helpers.encrypted_logger import get_encrypted_logger

# Use encrypted logger instead of console logger
encrypted_logger = get_encrypted_logger()
logger = Logger(
    level='INFO',
    formatting="{timestamp} [{level:^7}] ({name}) {message}",
    name="TasksManager"
)  # Keep for compatibility, but won't be used

# Memory optimization constants
MAX_CONCURRENT_TASKS = 500  # Maximum concurrent tasks supported
BATCH_SIZE = 50  # Default batch size for memory-efficient processing
MEMORY_LIMIT_MB = 1000  # Memory limit in MB before triggering cleanup

class MemoryMonitor:
    """
    Memory monitoring utility for tracking and optimizing memory usage.

    Provides real-time memory monitoring, cleanup triggers, and performance statistics
    for high-concurrency task management.
    """

    def __init__(self):
        """Initialize memory monitor."""
        self.process = psutil.Process()
        self.initial_memory = self.get_memory_usage()
        self.peak_memory = self.initial_memory
        self.samples = []
        self.cleanup_count = 0

    def get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        return self.process.memory_info().rss / 1024 / 1024

    def sample_memory(self, label: str = "") -> Dict[str, float]:
        """Take a memory sample and update peak usage."""
        current_memory = self.get_memory_usage()
        self.peak_memory = max(self.peak_memory, current_memory)

        sample = {
            'timestamp': time.time(),
            'memory_mb': current_memory,
            'increase_mb': current_memory - self.initial_memory,
            'label': label
        }

        self.samples.append(sample)

        # Keep only last 100 samples to prevent memory growth
        if len(self.samples) > 100:
            self.samples = self.samples[-100:]

        return sample

    def should_cleanup(self, limit_mb: float) -> bool:
        """Check if memory cleanup should be triggered."""
        return self.get_memory_usage() > limit_mb

    def force_cleanup(self):
        """Force garbage collection and memory cleanup."""
        self.cleanup_count += 1

        # Multiple garbage collection cycles for thorough cleanup
        for _ in range(3):
            gc.collect()

        # Sample memory after cleanup
        self.sample_memory(f"cleanup_{self.cleanup_count}")

    def get_stats(self) -> Dict[str, Any]:
        """Get memory statistics."""
        current_memory = self.get_memory_usage()

        return {
            'initial_mb': self.initial_memory,
            'current_mb': current_memory,
            'peak_mb': self.peak_memory,
            'increase_mb': current_memory - self.initial_memory,
            'peak_increase_mb': self.peak_memory - self.initial_memory,
            'cleanup_count': self.cleanup_count,
            'samples_count': len(self.samples)
        }

class QueueTaskManager:
    """
    Manages Queue-IT processing tasks with concurrent execution and real-time monitoring.

    This class implements a singleton pattern to manage Queue-IT bypass tasks across the
    application. It coordinates multiple concurrent tasks, monitors their progress, and
    provides real-time status updates through WebSocket communication.

    Key Features:
    - Singleton pattern for consistent state management
    - Concurrent task execution with configurable task counts
    - Real-time progress monitoring and status aggregation
    - Discord webhook notifications for pass links and completion
    - Thread-safe operations with proper cleanup
    - Integration with license validation and settings management
    - WebSocket broadcasting for frontend updates

    Task Processing Stages:
    - initializing: Setting up tasks and preparing for execution
    - solving: Actively working on queue bypass challenges
    - queued: Waiting in the queue system
    - waiting: Waiting for queue progression
    - active: Actively processing in the queue
    - completed: Successfully obtained required pass links
    - error: Failed due to errors or authentication issues

    Attributes:
        tasks (List[Dict]): List of active task entries
        logger (Logger): Task manager logger instance
        stop_event (threading.Event): Event for coordinating task shutdown
        _settings_manager (SettingsManager): Settings and configuration manager
        _is_authenticated (bool): Current authentication status

        # Task execution attributes
        task_id (str): Current task identifier
        task_count (int): Number of concurrent tasks to run
        ticket_count (int): Number of pass links required
        url (str): Queue-IT URL to process
        pass_links (List[str]): Successfully obtained pass links
        websocket_server: WebSocket server for broadcasting updates
        client_id (str): ID of the client that started the task
        event_loop: Asyncio event loop for scheduling async operations
        is_running (bool): Whether tasks are currently running
        start_time (float): Timestamp when tasks started
        overall_status (str): Aggregated status of all tasks
        last_update_time (float): Last time status was updated
        completion_time (float): Timestamp when tasks completed

    Example:
        >>> manager = QueueTaskManager()
        >>> manager.set_settings_manager(settings_manager)
        >>> manager.set_websocket_server(websocket_server)
        >>> result = await manager.start_task({
        ...     'task_id': 'task_123',
        ...     'ticket_count': 5,
        ...     'url': 'https://queue.example.com'
        ... })
    """
    _instance = None
    _initialized = False

    def __new__(cls):
        """
        Implement singleton pattern to ensure only one instance exists.

        Returns:
            QueueTaskManager: The singleton instance
        """
        if cls._instance is None:
            cls._instance = super(QueueTaskManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        """
        Initialize the QueueTaskManager singleton instance with memory optimizations.

        This method only initializes the instance once due to the singleton pattern.
        Subsequent calls to __init__ will not reinitialize the instance.
        """
        if not self._initialized:
            # Optimized data structure: Dict for O(1) lookup instead of List with O(n) lookup
            self.tasks = {}  # task_id -> task_data (was list with O(n) lookup)
            self.task_order = []  # Maintain insertion order for iteration
            self.logger = encrypted_logger
            self.stop_event = threading.Event()
            self._initialized = True
            self._is_authenticated = False

            # Task management attributes
            self.task_id = None
            self.task_count = 0
            self.ticket_count = 0
            self.url = None
            self.pass_links = []
            self.websocket_server = None
            self.client_id = None
            self.event_loop = None
            self.is_running = False
            self.start_time = None
            self.overall_status = 'initializing'
            self.last_update_time = time.time()
            self.completion_time = None
            self.final_update_sent = False  # Track if final update has been sent

            # Memory optimization attributes
            self.memory_monitor = MemoryMonitor()
            self.batch_size = BATCH_SIZE
            self.max_concurrent_tasks = MAX_CONCURRENT_TASKS
            self.memory_limit_mb = MEMORY_LIMIT_MB
            self._temp_data = weakref.WeakValueDictionary()  # Temporary data with weak references

            # Broadcast task tracking for proper cleanup
            self.broadcast_tasks = set()  # Track active broadcast tasks for cancellation
            self.monitoring_thread = None  # Track monitoring thread for cleanup

    def set_websocket_server(self, websocket_server):
        """
        Set the WebSocket server instance for broadcasting real-time updates.

        Args:
            websocket_server: The WebSocket server instance used to broadcast
                task progress updates, status changes, and completion notifications
                to connected clients in real-time.
        """
        self.websocket_server = websocket_server
        self.logger.info("WebSocket server reference set")

    def check_authentication(self):
        """
        Check if we have a valid license key for Queue-IT operations.

        This method validates that:
        1. A settings manager is configured
        2. License data exists in settings
        3. The license is marked as valid

        Returns:
            bool: True if authenticated and license is valid, False otherwise

        Note:
            This method is called before starting any Queue-IT operations to ensure
            proper authorization through the WHOP API license system.
        """
        self.logger.info("Checking authentication")

        settings = settings_manager.settings
        license_data = settings.get('license', {})

        # Check if we have a valid license
        if not license_data.get('valid'):
            self.logger.error("License is not valid")
            return False

        return True

    def ensure_authentication(self):
        """
        Ensure we're authenticated before allowing Queue-IT operations.

        This method performs authentication checking and automatically stops
        all running tasks if authentication fails. This provides security
        by preventing unauthorized Queue-IT processing.

        Returns:
            bool: True if authenticated, False if authentication failed

        Side Effects:
            - Stops all running tasks if authentication fails
            - Clears the task list if authentication fails

        Note:
            This method should be called before any task management operations
            to ensure proper authorization.
        """
        if not self.check_authentication():
            # Stop all tasks if we're not authenticated
            self.stop_all_tasks()
            # Clear all tasks (optimized: clear both dict and order list)
            self.tasks.clear()
            self.task_order.clear()
            return False
        return True

    async def start_task(self, task_data):
        """
        Start a new Queue-IT processing task with authentication and validation.

        This method initializes and starts a new queue bypass operation with the
        specified parameters. It performs authentication checking, sets up task
        state, and begins concurrent task execution.

        Args:
            task_data (Dict[str, Any]): Task configuration containing:
                - task_id (str, optional): Unique task identifier
                - task_count (int, optional): Number of concurrent tasks (default: 1)
                - ticket_count (int, optional): Number of pass links required (default: 1)
                - url (str): Queue-IT URL to process
                - client_id (str): ID of the client requesting the task

        Returns:
            Dict[str, Any]: Task start result containing:
                - success (bool): Whether the task started successfully
                - error (str): Error message if task failed to start
                - task_id (str): The assigned task identifier
                - task_count (int): Number of concurrent tasks started
                - status (str): Initial task status

        Raises:
            Exception: If task initialization or startup fails

        Note:
            This method resets all previous task state and starts fresh. Only one
            task can be active at a time per QueueTaskManager instance.
        """
        self.logger.info(f"Starting task with data: {task_data}")

        # Check authentication first
        if not self.check_authentication():
            return {
                'success': False,
                'error': 'Authentication required'
            }

        # Initialize task manager with the provided data
        self.task_id = task_data.get('task_id', f"task_{int(time.time())}")
        self.task_count = task_data.get('task_count', 1)
        self.amount = self.task_count  # Set amount for high concurrency detection
        self.ticket_count = task_data.get('ticket_count', 1)
        self.url = task_data.get('url', '')
        self.client_id = task_data.get('client_id')
        self.proxies = task_data.get('proxies', [])  # List of proxies from calculate_tasks

        # Reset state for new task (optimized: clear both dict and order list)
        self.tasks.clear()
        self.task_order.clear()
        self.pass_links.clear()
        self.is_running = False
        self.start_time = None
        self.overall_status = 'initializing'
        self.last_update_time = time.time()
        self.stop_event.clear()
        self.final_update_sent = False  # Track if final completion update has been sent

        # Get current event loop for async operations
        try:
            self.event_loop = asyncio.get_running_loop()
        except RuntimeError:
            self.event_loop = None
            self.logger.warning("No running event loop found")

        # Start the tasks
        return await self.start_tasks()

    def stop_all_tasks(self):
        """Stop all tasks with authentication check and memory cleanup."""
        if not self.ensure_authentication():
            return

        self.logger.info(f"Stopping all tasks for {self.task_id}")

        # STEP 1: Send final broadcast BEFORE stopping anything
        # This ensures clients receive the final status update
        self._send_final_broadcast_before_stop()

        # STEP 2: Set stop event to prevent new broadcast tasks from being scheduled
        self.stop_event.set()

        # STEP 3: Mark as not running to stop monitoring thread
        self.is_running = False

        # STEP 4: Give a brief moment for any in-flight broadcast tasks to see the stop flags
        # This prevents race conditions where broadcasts are scheduled just before stop
        import time
        time.sleep(0.1)

        # STEP 5: Cancel all active broadcast tasks to prevent unwanted updates
        self._cancel_broadcast_tasks()

        # Stop dedicated broadcast thread if it exists
        if hasattr(self, 'broadcast_thread') and self.broadcast_thread and self.broadcast_thread.is_alive():
            self.logger.info(f"🔥 CLEANUP: Stopping dedicated broadcast thread for task {self.task_id}")
            self.broadcast_thread.join(timeout=3.0)
            if self.broadcast_thread.is_alive():
                self.logger.warning(f"🔥 CLEANUP: Dedicated broadcast thread did not stop gracefully for task {self.task_id}")
            else:
                self.logger.info(f"🔥 CLEANUP: Dedicated broadcast thread stopped for task {self.task_id}")

        # Sample memory before cleanup
        self.memory_monitor.sample_memory("stop_tasks_start")

        # Fast cancellation for high concurrency scenarios
        # Use safe attribute access in case amount is not set
        task_count = getattr(self, 'amount', len(self.tasks))
        if task_count >= 100:
            stopped_count = self._fast_cancel_all_tasks()
        else:
            stopped_count = self._standard_cancel_all_tasks()

        # Clear stop event
        self.stop_event.clear()

        # Recalculate overall status BEFORE cleanup to preserve actual achieved status
        self.overall_status, self.error_message = self._calculate_overall_status()

        # Fast cleanup for high concurrency scenarios
        if task_count >= 100:
            self.logger.info(f"⚡ FAST CLEANUP: Performing fast cleanup for {stopped_count} tasks")
            # For high concurrency, just clear everything at once
            self.tasks.clear()
            self.task_order.clear()
            # Skip detailed memory monitoring for speed
            self.memory_monitor.force_cleanup()
        else:
            # Standard cleanup with detailed filtering for low concurrency
            self.logger.debug(f"🔄 STANDARD CLEANUP: Performing standard cleanup")
            active_tasks = {k: v for k, v in self.tasks.items() if v['status'] not in ['completed', 'stopped', 'error']}
            self.tasks = active_tasks
            self.task_order = [task_id for task_id in self.task_order if task_id in active_tasks]
            self.memory_monitor.force_cleanup()
            self.memory_monitor.sample_memory("stop_tasks_complete")

        self.logger.info(f"Stopped {stopped_count} tasks, memory cleanup completed")
        self.logger.info(f"Final overall status after stopping: {self.overall_status}")

        return {
            'success': True,
            'message': f'Stopped {stopped_count} tasks',
            'task_id': self.task_id,
            'status': self.overall_status,  # Use actual calculated status instead of 'stopped'
            'memory_stats': self.memory_monitor.get_stats()
        }

    def get_tasks(self):
        """Get all tasks with authentication check (optimized: return dict values as list)."""
        if not self.ensure_authentication():
            return []
        return list(self.tasks.values())

    def get_task(self, task_id):
        """Get a specific task with authentication check (optimized: O(1) dict lookup)."""
        if not self.ensure_authentication():
            return None
        return self.tasks.get(task_id)

    def get_status_summary(self):
        """Get a summary of the current task status."""
        if not self.ensure_authentication():
            return {
                'error': 'Authentication required',
                'status': 'unauthenticated'
            }

        return {
            'task_id': self.task_id,
            'overall_status': self.overall_status,
            'task_count': self.task_count,
            'ticket_count': self.ticket_count,
            'pass_links_count': len(self.pass_links),
            'pass_links': self.pass_links,
            'url': self.url,
            'is_running': self.is_running,
            'start_time': self.start_time,
            'completion_time': getattr(self, 'completion_time', None),
            'duration': time.time() - self.start_time if self.start_time else 0,
            'error_message': getattr(self, 'error_message', None),
            'tasks': [
                {
                    'task_index': task['task_index'],
                    'status': task['status'],
                    'error': task['error'],
                    'duration': time.time() - task['start_time']
                }
                for task in self.tasks.values()  # Optimized: iterate over dict values
            ]
        }

    def _schedule_async_task(self, coro, track_broadcast=False):
        """
        Thread-safe method to schedule async operations from worker threads.

        Args:
            coro: The coroutine to schedule
            track_broadcast: If True, track this task for cancellation (used for broadcast tasks)
        """
        if self.event_loop and not self.event_loop.is_closed():
            try:
                # Enhanced debugging for broadcast tasks
                if track_broadcast:
                    if self.amount >= 500:
                        # For high concurrency, log at info level to track scheduling
                        self.logger.info(f"📡 SCHEDULE: Scheduling broadcast task for task {self.task_id}, client_id={self.client_id}")
                    else:
                        self.logger.debug(f"📡 SCHEDULE: Scheduling broadcast task for task {self.task_id}, client_id={self.client_id}")

                # Check if event loop is running before scheduling
                if not self.event_loop.is_running():
                    self.logger.warning(f"⚠️ SCHEDULE: Event loop not running for task {self.task_id}")
                    return None

                # Schedule the coroutine to run in the event loop from another thread
                future = asyncio.run_coroutine_threadsafe(coro, self.event_loop)

                # Track broadcast tasks for proper cleanup
                if track_broadcast:
                    self.broadcast_tasks.add(future)
                    if self.amount >= 500:
                        self.logger.debug(f"📡 SCHEDULE: Added broadcast task to tracking set (total: {len(self.broadcast_tasks)}) - high concurrency mode")
                    else:
                        self.logger.debug(f"📡 SCHEDULE: Added broadcast task to tracking set (total: {len(self.broadcast_tasks)})")

                    # Remove from tracking when done and add completion callback
                    def on_broadcast_complete(f):
                        self.broadcast_tasks.discard(f)
                        try:
                            # Add timeout for high concurrency scenarios
                            if self.amount >= 500:
                                # For high concurrency, don't wait for result to avoid blocking
                                if f.done():
                                    result = f.result()
                                    self.logger.debug(f"✅ SCHEDULE: Broadcast task completed for task {self.task_id}")
                            else:
                                result = f.result()
                                self.logger.debug(f"✅ SCHEDULE: Broadcast task completed successfully for task {self.task_id}")
                        except Exception as e:
                            if self.amount >= 500:
                                self.logger.warning(f"⚠️ SCHEDULE: Broadcast task failed for task {self.task_id}: {e}")
                            else:
                                self.logger.error(f"❌ SCHEDULE: Broadcast task failed for task {self.task_id}: {e}")

                    future.add_done_callback(on_broadcast_complete)

                return future
            except Exception as e:
                self.logger.error(f"❌ SCHEDULE: Failed to schedule async task: {e}")
        else:
            self.logger.warning(f"⚠️ SCHEDULE: No event loop available to schedule async task for task {self.task_id}")
        return None

    def _cancel_broadcast_tasks(self):
        """Cancel all active broadcast tasks to prevent memory leaks and unwanted updates."""
        if self.broadcast_tasks:
            self.logger.info(f"Cancelling {len(self.broadcast_tasks)} active broadcast tasks")
            cancelled_count = 0

            # Create a copy of the set to avoid modification during iteration
            tasks_to_cancel = self.broadcast_tasks.copy()

            for task in tasks_to_cancel:
                try:
                    if not task.done():
                        task.cancel()
                        cancelled_count += 1
                except Exception as e:
                    self.logger.warning(f"Error cancelling broadcast task: {e}")

            # Clear the tracking set
            self.broadcast_tasks.clear()

            if cancelled_count > 0:
                self.logger.info(f"Successfully cancelled {cancelled_count} broadcast tasks")
        else:
            self.logger.debug("No active broadcast tasks to cancel")

    async def start_tasks(self) -> Dict[str, Any]:
        """Launch all concurrent queue bypass tasks with memory optimization"""
        try:
            self.is_running = True
            self.start_time = time.time()

            # Sample initial memory
            self.memory_monitor.sample_memory("task_start")

            self.logger.info(f"Starting {self.task_count} memory-optimized queue bypass tasks for {self.task_id}")

            # Use batch processing for high task counts to manage memory
            if self.task_count > self.batch_size:
                await self._start_tasks_in_batches()
            else:
                # Create and start all tasks for smaller counts
                for i in range(self.task_count):
                    await self._create_task(i)

            # Start monitoring thread and track it for cleanup
            self.monitoring_thread = Thread(target=self._monitor_tasks, daemon=True)
            self.monitoring_thread.start()
            self.logger.info(f"Started monitoring thread for task {self.task_id}")

            # Start dedicated broadcast thread for high concurrency scenarios
            if self.amount >= 100:  # Use dedicated broadcast thread for high concurrency
                self.broadcast_thread = Thread(target=self._dedicated_broadcast_loop, daemon=True)
                self.broadcast_thread.start()
                self.logger.info(f"🔥 HIGH CONCURRENCY: Started dedicated broadcast thread for task {self.task_id} ({self.amount} tasks)")
            else:
                self.broadcast_thread = None

            # Send initial status update if websocket server is available (but don't wait for it)
            if self.websocket_server:
                if self.amount >= 100:
                    # For high concurrency, let the dedicated broadcast thread handle this
                    self.logger.info(f"🔥 HIGH CONCURRENCY: Initial broadcast will be handled by dedicated broadcast thread for task {self.task_id}")
                else:
                    # For low concurrency, use the original method
                    initial_broadcast_task = asyncio.create_task(self._broadcast_update())
                    self.broadcast_tasks.add(initial_broadcast_task)
                    # Remove from tracking when done
                    initial_broadcast_task.add_done_callback(lambda f: self.broadcast_tasks.discard(f))
                    self.logger.info(f"Scheduled initial status update for task {self.task_id}")

            # Sample memory after task creation
            self.memory_monitor.sample_memory("tasks_created")

            return {
                'success': True,
                'task_id': self.task_id,
                'task_count': self.task_count,
                'status': 'started',
                'memory_stats': self.memory_monitor.get_stats()
            }

        except Exception as e:
            self.logger.error(f"Failed to start tasks for {self.task_id}: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    async def _start_tasks_in_batches(self):
        """Start tasks in batches for memory-efficient processing."""
        total_batches = (self.task_count + self.batch_size - 1) // self.batch_size

        self.logger.info(f"Starting {self.task_count} tasks in {total_batches} batches of {self.batch_size}")

        for batch_num in range(total_batches):
            start_idx = batch_num * self.batch_size
            end_idx = min(start_idx + self.batch_size, self.task_count)

            self.logger.info(f"Creating batch {batch_num + 1}/{total_batches}: tasks {start_idx}-{end_idx-1}")

            # Sample memory before batch
            self.memory_monitor.sample_memory(f"batch_{batch_num}_start")

            # Create tasks in this batch
            batch_tasks = []
            for i in range(start_idx, end_idx):
                task_creation = self._create_task(i)
                batch_tasks.append(task_creation)

            # Wait for batch creation to complete
            await asyncio.gather(*batch_tasks)

            # Sample memory after batch
            self.memory_monitor.sample_memory(f"batch_{batch_num}_end")

            # Check memory usage and cleanup if needed
            if self.memory_monitor.should_cleanup(self.memory_limit_mb):
                self.logger.warning(f"Memory limit reached after batch {batch_num + 1}, performing cleanup")
                self.memory_monitor.force_cleanup()

            # Small delay between batches to allow memory cleanup and reduce connection pressure
            if batch_num < total_batches - 1:  # Don't delay after last batch
                # Longer delay for high concurrency to prevent connection exhaustion
                delay = 1.0 if self.amount > 100 else 0.5
                await asyncio.sleep(delay)

    async def _create_task(self, task_index: int):
        """Create and start a single memory-optimized queue bypass task"""
        try:
            self.logger.debug(f"Creating memory-optimized task {task_index}")

            # Get settings for captcha API keys (cache to avoid repeated access)
            if not hasattr(self, '_cached_settings'):
                self._cached_settings = settings_manager.settings
            settings = self._cached_settings

            # Assign unique proxy to this task if available
            task_proxy = None
            if self.proxies and len(self.proxies) > 0:
                # Assign proxy using round-robin distribution
                proxy_index = task_index % len(self.proxies)
                task_proxy = self.proxies[proxy_index]
                self.logger.debug(f"Assigned proxy {proxy_index + 1}/{len(self.proxies)} to task {task_index}: {task_proxy}")

            # Create AsyncQueueItHandler instance with memory optimizations
            # Use None for session parameter (1:1 compatibility with sync version)
            handler = AsyncQueueItHandler(
                session=None,
                url=self.url,
                license_key=settings.get("license", {}).get('license_key', ''),
                hwid=settings.get("license", {}).get('metadata', {}).get('hwid', ''),
                proxy=task_proxy,  # Assign unique proxy to this task
                use_encryption=True  # Enable encryption for solver API calls
            )

            # Create memory-efficient task entry with minimal data
            current_time = time.time()
            task_entry = {
                'handler': handler,
                'task_index': task_index,
                'status': 'initializing',
                'error': None,
                'start_time': current_time,
                'last_status_change': current_time,
                'thread': None,  # No thread needed for async
                'memory_optimized': True  # Flag for memory-optimized tasks
            }

            # Generate memory-efficient task ID
            task_id = f"task_{task_index}_{int(current_time * 1000) % 1000000}"

            # Add task to optimized dict structure (O(1) insertion)
            self.tasks[task_id] = task_entry
            self.task_order.append(task_id)
            task_entry['id'] = task_id  # Store task ID for reference

            # Start task as async task (no threading needed)
            task = asyncio.create_task(self._run_task_async(task_entry))
            task_entry['async_task'] = task

            self.logger.debug(f"Created memory-optimized task {task_index} with ID {task_id}")

        except Exception as e:
            self.logger.error(f"Failed to create task {task_index}: {e}", exc_info=True)
    
    async def _send_discord_webhook(self, pass_url: str, task_index: int):
        """Send a Discord webhook notification with pass URL details."""
        try:

            settings = settings_manager.settings
            webhook_config = settings.get('webhook', {})

            if not webhook_config.get('enabled') or not webhook_config.get('url'):
                self.logger.debug("Discord webhook not configured or disabled")
                return

            # Prepare webhook payload with improved format
            payload = {
                "content": None,
                "embeds": [{
                    "title": "🎫 Queue Pass Acquired",
                    "description": f"**Task `#{task_index}` completed.** Pass link successfully retrieved.",
                    "color": 0x181122,  # Custom color #181122
                    "fields": [
                        {
                            "name": "🔗 Pass Link",
                            "value": f"[Click here]({pass_url})"
                        },
                        {
                            "name": "🌍 Event URL",
                            "value": f"[Open Event]({self.url})"
                        }
                    ],
                    "footer": {
                        "text": f"pass url #{len(self.pass_links)}/{self.ticket_count}",
                        "icon_url": "https://i.imgur.com/Z9ersnW.png"
                    },
                    "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S.000Z', time.gmtime())
                }],
                "username": "Kasper-Q",
                "avatar_url": "https://i.imgur.com/Z9ersnW.png",
                "attachments": []
            }

            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_config['url'],
                    json=payload,
                    timeout=10
                ) as response:
                    if response.status == 204:
                        self.logger.info(f"Discord webhook sent successfully for task {task_index}")
                    else:
                        self.logger.warning(f"Failed to send Discord webhook for task {task_index}: {response.status}")
                        response_text = await response.text()
                        self.logger.debug(f"Webhook response: {response_text}")

        except Exception as e:
            self.logger.error(f"Error sending Discord webhook: {e}")

    async def _send_completion_webhook(self):
        """Send a Discord webhook notification when all pass links are obtained."""
        try:
            settings = settings_manager.settings
            webhook_config = settings.get('webhook', {})

            if not webhook_config.get('enabled') or not webhook_config.get('url'):
                self.logger.debug("Discord webhook not configured or disabled")
                return

            # Calculate duration
            duration = time.time() - self.start_time if self.start_time else 0
            duration_str = f"{int(duration // 60)}m {int(duration % 60)}s"

            # Prepare webhook payload with improved format
            payload = {
                "content": None,
                "embeds": [{
                    "title": "✅ Queue Entry Completed",
                    "description": f"**All {self.ticket_count} pass links successfully obtained.** Task completed in {duration_str}.",
                    "color": 0x181122,  # Custom color #181122
                    "fields": [
                        {
                            "name": "🔗 Pass Links",
                            "value": "\n".join([f"[Link {i+1}]({link})" for i, link in enumerate(self.pass_links)])
                        },
                        {
                            "name": "🌍 Event URL",
                            "value": f"[Open Event]({self.url})"
                        },
                        {
                            "name": "⏱️ Duration",
                            "value": duration_str
                        }
                    ],
                    "footer": {
                        "text": f"Queue Entry Complete • {self.ticket_count} tickets",
                        "icon_url": "https://i.imgur.com/Z9ersnW.png"
                    },
                    "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S.000Z', time.gmtime())
                }],
                "username": "Kasper-Q",
                "avatar_url": "https://i.imgur.com/Z9ersnW.png",
                "attachments": []
            }

            # Send webhook
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    webhook_config['url'],
                    json=payload,
                    timeout=10
                ) as response:
                    if response.status == 204:
                        self.logger.info("Discord webhook sent successfully for completion")
                    else:
                        self.logger.warning(f"Failed to send completion Discord webhook: {response.status}")
                        response_text = await response.text()
                        self.logger.debug(f"Webhook response: {response_text}")

        except Exception as e:
            self.logger.error(f"Error sending completion Discord webhook: {e}")

    def _run_task(self, task_entry: Dict[str, Any]):
        """Run individual queue bypass task with progress monitoring"""
        handler = task_entry['handler']
        task_index = task_entry['task_index']
        
        try:
            self.logger.info(f"Starting queue bypass task {task_index}")
            
            # Update status to solving before starting the process
            task_entry['status'] = 'solving'
            task_entry['last_status_change'] = time.time()
            
            # Start the queue bypass process in a new thread
            solve_thread = Thread(target=handler.solve, daemon=True)
            solve_thread.start()
            
            # Monitor task progress
            last_status = None
            last_check_time = time.time()
            
            while not self.stop_event.is_set() and self.is_running:
                # Check stop event first
                if self.stop_event.is_set():
                    self.logger.info(f"Stop event detected for task {task_index}")
                    task_entry['status'] = 'stopped'
                    task_entry['error'] = 'Task stopped by user'
                    # Force stop the solve thread
                    if solve_thread.is_alive():
                        thread_id = solve_thread.ident
                        if thread_id:
                            # Force terminate the thread
                            ctypes.pythonapi.PyThreadState_SetAsyncExc(
                                ctypes.c_long(thread_id),
                                ctypes.py_object(SystemExit)
                            )
                    break

                current_status = handler.status
                current_error = handler.error
                
                # Log status periodically (every 5 seconds) or on change
                current_time = time.time()
                if current_status != last_status or current_time - last_check_time >= 5:
                    self.logger.debug(f"Task {task_index} status: {current_status} (error: {current_error})")
                    last_check_time = current_time
                
                # Update task status if changed
                if current_status != last_status:
                    task_entry['status'] = current_status
                    task_entry['error'] = current_error
                    task_entry['last_status_change'] = time.time()
                    
                    self.logger.info(f"Task {task_index} status changed: {last_status} -> {current_status}")
                    last_status = current_status
                
                # Check if task obtained a new passlink
                if handler.pass_url and handler.pass_url not in self.pass_links:
                    self.pass_links.append(handler.pass_url)
                    task_entry['pass_url'] = handler.pass_url

                    self.logger.info(f"Task {task_index} obtained passlink: {handler.pass_url} ({len(self.pass_links)}/{self.ticket_count})")

                    # Send Discord webhook for individual pass link
                    self._schedule_async_task(self._send_discord_webhook(handler.pass_url, task_index))

                    # Check if we have enough pass-links for the entire operation
                    if len(self.pass_links) >= self.ticket_count:
                        self.logger.info(f"Target passlinks reached ({len(self.pass_links)}/{self.ticket_count}) - completing all tasks")
                        # Mark this individual task as completed since we got a passlink
                        task_entry['status'] = 'completed'
                        # Schedule completion of the entire operation
                        self._schedule_async_task(self._complete_all_tasks())
                        break
                    else:
                        # We need more passlinks - create a new handler instance for this task
                        self.logger.info(f"Task {task_index} needs to continue for more passlinks ({len(self.pass_links)}/{self.ticket_count}) - creating new handler")

                        # Get settings for new handler
                        settings = settings_manager.settings

                        # Get the same proxy that was assigned to this task originally
                        task_proxy = None
                        if self.proxies and len(self.proxies) > 0:
                            proxy_index = task_index % len(self.proxies)
                            task_proxy = self.proxies[proxy_index]

                        # Create new AsyncQueueItHandler instance (async version for better performance)
                        # Use None for session parameter (1:1 compatibility with sync version)
                        new_handler = AsyncQueueItHandler(
                            session=None,
                            url=self.url,
                            license_key=settings.get("license", {}).get('license_key', ''),
                            hwid=settings.get("license", {}).get('metadata', {}).get('hwid', ''),
                            proxy=task_proxy,  # Use same proxy as original task
                            use_encryption=True  # Enable encryption for solver API calls
                        )

                        # Replace the handler in the task entry
                        task_entry['handler'] = new_handler
                        task_entry['status'] = 'solving'
                        task_entry['last_status_change'] = time.time()

                        # Start new solve thread
                        solve_thread = Thread(target=new_handler.solve, daemon=True)
                        solve_thread.start()

                        self.logger.info(f"Task {task_index} restarted with new handler for additional passlinks")
                
                # Check for error states
                if current_status == 'error' and current_error:
                    self.logger.warning(f"Task {task_index} failed: {current_error}")
                    break
                
                # Check if solve thread is still alive
                if not solve_thread.is_alive():
                    if current_status not in ['completed', 'error']:
                        self.logger.warning(f"Task {task_index} solve thread died unexpectedly")
                        task_entry['status'] = 'error'
                        task_entry['error'] = "Solve thread died unexpectedly"
                        break
                
                time.sleep(0.5)  # Status check interval
                
        except Exception as e:
            self.logger.error(f"Task {task_index} encountered exception: {e}")
            task_entry['status'] = 'error'
            task_entry['error'] = str(e)

    async def _run_task_async(self, task_entry: Dict[str, Any]):
        """Run individual queue bypass task asynchronously with memory-optimized progress monitoring"""
        handler = task_entry['handler']
        task_index = task_entry['task_index']

        try:
            self.logger.debug(f"Starting memory-optimized async queue bypass task {task_index}")

            # Update status to solving before starting the process
            task_entry['status'] = 'solving'
            task_entry['last_status_change'] = time.time()

            # Start the async queue bypass process
            solve_task = asyncio.create_task(handler.solve())

            # Memory-optimized monitoring with reduced frequency
            last_status = None
            last_check_time = time.time()
            status_check_interval = 1.0  # Reduced from 0.5 to save CPU/memory

            while not self.stop_event.is_set() and self.is_running:
                # Check stop event first
                if self.stop_event.is_set():
                    self.logger.debug(f"Stop event detected for async task {task_index}")
                    task_entry['status'] = 'stopped'
                    task_entry['error'] = 'Task stopped by user'

                    # Gracefully stop the handler first
                    if hasattr(handler, 'stop'):
                        handler.stop()

                    # Cancel the async task with grace period
                    if not solve_task.done():
                        solve_task.cancel()
                        try:
                            # Give the task a moment to handle cancellation gracefully
                            await asyncio.wait_for(solve_task, timeout=2.0)
                        except (asyncio.CancelledError, asyncio.TimeoutError):
                            self.logger.debug(f"Async task {task_index} cancelled successfully")
                        except Exception as e:
                            self.logger.debug(f"Async task {task_index} cancelled with exception: {e}")
                    break

                current_status = handler.status
                current_error = handler.error

                # Log status periodically (every 10 seconds) or on change - reduced frequency
                current_time = time.time()
                if current_status != last_status or current_time - last_check_time >= 10:
                    self.logger.debug(f"Async task {task_index} status: {current_status}")
                    last_check_time = current_time

                # Update task status if changed
                if current_status != last_status:
                    task_entry['status'] = current_status
                    task_entry['last_status_change'] = current_time
                    last_status = current_status
                    self.logger.debug(f"Async task {task_index} status: {current_status}")

                # Check for completion with pass URL
                if current_status == 'completed' and handler.pass_url:
                    self.logger.info(f"Async task {task_index} completed with pass URL")

                    # Add pass URL if not already present
                    if handler.pass_url not in self.pass_links:
                        self.pass_links.append(handler.pass_url)
                        task_entry['pass_url'] = handler.pass_url

                        # Send Discord webhook for individual pass link
                        self._schedule_async_task(self._send_discord_webhook(handler.pass_url, task_index))

                        # Check if we have enough pass-links
                        if len(self.pass_links) >= self.ticket_count:
                            self.logger.info(f"Target passlinks reached ({len(self.pass_links)}/{self.ticket_count})")
                            task_entry['status'] = 'completed'
                            self._schedule_async_task(self._complete_all_tasks())
                            break
                    break

                # Check for error states
                if current_status == 'error' and current_error:
                    self.logger.warning(f"Async task {task_index} failed: {current_error}")
                    task_entry['error'] = current_error
                    break

                # Check if solve task is done
                if solve_task.done():
                    try:
                        result = solve_task.result()
                        if result:
                            self.logger.debug(f"Async task {task_index} solve completed")
                            # Handle pass URL from result if available
                            if hasattr(handler, 'pass_url') and handler.pass_url:
                                if handler.pass_url not in self.pass_links:
                                    self.pass_links.append(handler.pass_url)
                                    task_entry['pass_url'] = handler.pass_url
                        else:
                            self.logger.debug(f"Async task {task_index} solve completed without result")
                    except Exception as e:
                        self.logger.error(f"Async task {task_index} solve failed: {e}")
                        task_entry['status'] = 'error'
                        task_entry['error'] = str(e)
                    break

                # Memory-optimized sleep interval
                await asyncio.sleep(status_check_interval)

        except asyncio.CancelledError:
            self.logger.debug(f"Async task {task_index} was cancelled")
            task_entry['status'] = 'cancelled'
            task_entry['error'] = 'Task was cancelled'
        except Exception as e:
            self.logger.error(f"Async task {task_index} encountered exception: {e}")
            task_entry['status'] = 'error'
            task_entry['error'] = str(e)
        finally:
            # Memory cleanup for completed task
            await self._cleanup_task(task_entry)

    async def _cleanup_task(self, task_entry: Dict[str, Any]):
        """Clean up task resources to free memory. Optimized for high concurrency."""
        try:
            # Skip individual cleanup for high concurrency scenarios
            # The batch cleanup will handle everything more efficiently
            if self.amount >= 100:
                # For high concurrency, skip detailed individual cleanup
                # Just clear references to prevent memory leaks
                task_entry.update({'handler': None, 'async_task': None, 'thread': None})
                return

            # Standard detailed cleanup for low concurrency only
            task_index = task_entry.get('task_index', 'unknown')
            self.logger.debug(f"Cleaning up task {task_index}")

            # Clean up handler resources
            handler = task_entry.get('handler')
            if handler:
                try:
                    # Call handler cleanup if available
                    if hasattr(handler, 'cleanup'):
                        await handler.cleanup()

                    # Clear handler reference
                    task_entry['handler'] = None
                except Exception as e:
                    self.logger.error(f"Error cleaning up handler for task {task_index}: {e}")

            # Cancel async task if still running
            async_task = task_entry.get('async_task')
            if async_task and not async_task.done():
                async_task.cancel()
                try:
                    await async_task
                except asyncio.CancelledError:
                    pass

            # Clear large objects from task entry
            for key in ['async_task', 'thread']:
                if key in task_entry:
                    task_entry[key] = None

            # Force garbage collection for this task (only for low concurrency)
            gc.collect()

        except Exception as e:
            if self.amount < 100:  # Only log errors for low concurrency
                self.logger.error(f"Error during task cleanup: {e}")

    def _dedicated_broadcast_loop(self):
        """
        Dedicated broadcast thread for high concurrency scenarios (100+ tasks).
        This runs independently of the monitoring thread to ensure broadcasts
        are sent even when the monitoring thread is overwhelmed.
        """
        self.logger.info(f"🔥 BROADCAST THREAD: Starting dedicated broadcast loop for task {self.task_id}")

        # Adaptive intervals based on task count
        if self.amount >= 500:
            broadcast_interval = 3.0  # Very high concurrency - slower broadcasts
            self.logger.info(f"🔥 BROADCAST THREAD: Very high concurrency mode ({self.amount} tasks) - {broadcast_interval}s intervals")
        elif self.amount >= 200:
            broadcast_interval = 2.0  # High concurrency - medium broadcasts
            self.logger.info(f"🔥 BROADCAST THREAD: High concurrency mode ({self.amount} tasks) - {broadcast_interval}s intervals")
        else:
            broadcast_interval = 1.0  # Medium concurrency - fast broadcasts
            self.logger.info(f"🔥 BROADCAST THREAD: Medium concurrency mode ({self.amount} tasks) - {broadcast_interval}s intervals")

        last_broadcast_time = 0
        broadcast_count = 0

        while self.is_running and not self.stop_event.is_set():
            try:
                current_time = time.time()

                # Check if it's time to broadcast
                if current_time - last_broadcast_time >= broadcast_interval:

                    # Check if we should stop broadcasting
                    if self._should_stop_broadcasting():
                        self.logger.info(f"🔥 BROADCAST THREAD: Stopping broadcasts for task {self.task_id} - task in terminal state")
                        break

                    # Force broadcast using asyncio.run to create a new event loop
                    try:
                        self.logger.info(f"🔥 BROADCAST THREAD: Sending broadcast #{broadcast_count + 1} for task {self.task_id} (status={self.overall_status})")

                        # Create a new event loop for this thread
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        try:
                            # Run the broadcast in the new event loop
                            loop.run_until_complete(self._broadcast_update())
                            broadcast_count += 1
                            last_broadcast_time = current_time
                            self.logger.info(f"🔥 BROADCAST THREAD: Successfully sent broadcast #{broadcast_count} for task {self.task_id}")

                        finally:
                            loop.close()

                    except Exception as broadcast_error:
                        self.logger.error(f"🔥 BROADCAST THREAD: Failed to send broadcast for task {self.task_id}: {broadcast_error}")

                # Sleep for a short interval to avoid busy waiting
                time.sleep(0.5)

            except Exception as e:
                self.logger.error(f"🔥 BROADCAST THREAD: Error in dedicated broadcast loop for task {self.task_id}: {e}")
                time.sleep(1.0)  # Wait longer on error

        self.logger.info(f"🔥 BROADCAST THREAD: Dedicated broadcast loop stopped for task {self.task_id} (sent {broadcast_count} broadcasts)")

    def _fast_cancel_all_tasks(self) -> int:
        """
        Fast cancellation method for high concurrency scenarios (100+ tasks).
        Uses true batch operations with minimal logging for maximum performance.
        """
        start_time = time.time()
        task_count = len(self.tasks)
        self.logger.info(f"⚡ FAST CANCEL: Starting ultra-fast batch cancellation for {task_count} tasks")

        # Step 1: Collect all resources in a single pass (no individual processing)
        async_tasks_to_cancel = []
        handlers_to_stop = []
        threads_to_stop = []

        # Single loop to collect everything - no individual task processing
        for task in self.tasks.values():
            if task.get('async_task') and not task['async_task'].done():
                async_tasks_to_cancel.append(task['async_task'])
            if task.get('handler'):
                handlers_to_stop.append(task['handler'])
            if task.get('thread') and task['thread'] and task['thread'].is_alive():
                threads_to_stop.append(task['thread'])

        # Step 2: Ultra-fast batch cancellation - no individual error handling
        if async_tasks_to_cancel:
            self.logger.info(f"⚡ FAST CANCEL: Batch cancelling {len(async_tasks_to_cancel)} async tasks")
            # Cancel all at once with list comprehension for speed
            [task.cancel() for task in async_tasks_to_cancel if not task.done()]

        # Step 3: Ultra-fast batch handler stopping - no individual error handling
        if handlers_to_stop:
            self.logger.info(f"⚡ FAST CANCEL: Batch stopping {len(handlers_to_stop)} handlers")
            # Stop all handlers at once
            [handler.stop() for handler in handlers_to_stop if hasattr(handler, 'stop')]

        # Step 4: Ultra-fast thread termination (if any legacy threads exist)
        if threads_to_stop:
            self.logger.debug(f"⚡ FAST CANCEL: Force terminating {len(threads_to_stop)} legacy threads")
            # Terminate all threads at once
            for thread in threads_to_stop:
                if thread.ident:
                    try:
                        ctypes.pythonapi.PyThreadState_SetAsyncExc(
                            ctypes.c_long(thread.ident), ctypes.py_object(SystemExit)
                        )
                    except:
                        pass

        # Step 5: Ultra-fast batch status update - single timestamp for all
        current_time = time.time()

        # Batch update all task properties at once - preserve completed/error tasks
        for task in self.tasks.values():
            # Only mark non-terminal tasks as stopped, preserve completed/error status
            if task['status'] not in ['completed', 'error']:
                task.update({
                    'status': 'stopped',
                    'error': 'Task stopped by user',
                    'last_status_change': current_time,
                    'handler': None,
                    'async_task': None,
                    'thread': None
                })
            else:
                # For completed/error tasks, only clear resources but preserve status
                task.update({
                    'handler': None,
                    'async_task': None,
                    'thread': None
                })

        elapsed_time = time.time() - start_time
        self.logger.info(f"⚡ FAST CANCEL: Ultra-fast batch cancellation completed - {task_count} tasks in {elapsed_time:.3f}s ({task_count/elapsed_time:.0f} tasks/sec)")

        return task_count

    def _standard_cancel_all_tasks(self) -> int:
        """
        Standard cancellation method for low/medium concurrency scenarios (<100 tasks).
        Provides detailed logging and error handling.
        """
        self.logger.debug(f"🔄 STANDARD CANCEL: Starting standard cancellation for {len(self.tasks)} tasks")

        stopped_count = 0
        for task in self.tasks.values():
            try:
                # Stop the AsyncQueueItHandler first
                if task.get('handler'):
                    task['handler'].stop()

                # Cancel async task if it exists
                if task.get('async_task') and not task['async_task'].done():
                    task['async_task'].cancel()
                    self.logger.debug(f"Cancelled async task for task index {task.get('task_index', 'unknown')}")

                # Legacy thread handling (for backward compatibility)
                if task.get('thread') and task['thread'] and task['thread'].is_alive():
                    thread_id = task['thread'].ident
                    if thread_id:
                        # Raise SystemExit in the thread
                        ctypes.pythonapi.PyThreadState_SetAsyncExc(
                            ctypes.c_long(thread_id),
                            ctypes.py_object(SystemExit)
                        )

                # Update task status - preserve completed/error tasks
                if task['status'] not in ['completed', 'error']:
                    task['status'] = 'stopped'
                    task['error'] = 'Task stopped by user'
                    task['last_status_change'] = time.time()

                # Clear handler reference for memory cleanup
                task['handler'] = None
                task['async_task'] = None
                task['thread'] = None

                stopped_count += 1

            except Exception as e:
                self.logger.error(f"Error stopping task {task.get('task_index', 'unknown')}: {e}")

        self.logger.info(f"🔄 STANDARD CANCEL: Stopped {stopped_count} tasks")
        return stopped_count

    def _monitor_tasks(self):
        """Monitor overall task progress and broadcast updates with memory optimization"""
        monitor_interval = 1.0  # Increased from 0.5 to reduce CPU/memory usage

        # Adaptive broadcast interval based on task count for high concurrency
        if self.amount >= 500:
            broadcast_interval = 2.0  # Slower broadcasts for very high concurrency
            self.logger.info(f"🔍 MONITOR: High concurrency mode ({self.amount} tasks) - broadcast interval={broadcast_interval}s")
        elif self.amount >= 100:
            broadcast_interval = 1.0  # Medium broadcasts for high concurrency
            self.logger.debug(f"🔍 MONITOR: Medium concurrency mode ({self.amount} tasks) - broadcast interval={broadcast_interval}s")
        else:
            broadcast_interval = 0.5  # Fast broadcasts for low concurrency
            self.logger.debug(f"🔍 MONITOR: Low concurrency mode ({self.amount} tasks) - broadcast interval={broadcast_interval}s")

        memory_check_interval = 10.0  # Check memory every 10 seconds
        last_memory_check = time.time()

        # Track broadcast failures for high concurrency debugging
        broadcast_failures = 0
        last_successful_broadcast = time.time()

        while self.is_running and not self.stop_event.is_set():
            try:
                current_time = time.time()

                # Calculate overall progress
                old_status = self.overall_status
                self.overall_status, error_message = self._calculate_overall_status()

                # Store error message if status is error
                if self.overall_status == 'error':
                    self.error_message = error_message
                elif hasattr(self, 'error_message'):
                    delattr(self, 'error_message')

                # Check if task is completed with all required passlinks
                is_completed = self._is_task_fully_completed()

                # Memory monitoring and cleanup
                if current_time - last_memory_check >= memory_check_interval:
                    self.memory_monitor.sample_memory("monitor_check")

                    if self.memory_monitor.should_cleanup(self.memory_limit_mb):
                        self.logger.info("Memory limit reached during monitoring, performing cleanup")
                        self.memory_monitor.force_cleanup()

                    last_memory_check = current_time

                # Broadcast update if status changed or at broadcast interval
                should_broadcast = (self.overall_status != old_status or
                                  current_time - self.last_update_time > broadcast_interval)

                # Only schedule broadcast if we should broadcast AND broadcasting should continue
                # BUT skip if dedicated broadcast thread is handling broadcasts
                if should_broadcast and not self._should_stop_broadcasting():

                    if self.amount >= 100 and hasattr(self, 'broadcast_thread') and self.broadcast_thread and self.broadcast_thread.is_alive():
                        # Dedicated broadcast thread is handling broadcasts for high concurrency
                        self.logger.debug(f"🔥 MONITOR: Skipping broadcast - dedicated broadcast thread is handling for task {self.task_id}")
                        self.last_update_time = current_time  # Update time to prevent spam
                    else:
                        # Enhanced broadcast scheduling for low/medium concurrency
                        try:
                            if self.amount >= 500:
                                # For very high concurrency, log at info level to track broadcasts
                                self.logger.info(f"📡 MONITOR: Scheduling broadcast for task {self.task_id} (status={self.overall_status}, pass_links={len(self.pass_links)}/{self.ticket_count})")
                            else:
                                self.logger.debug(f"📡 MONITOR: Scheduling broadcast for task {self.task_id} (status={self.overall_status}, pass_links={len(self.pass_links)}/{self.ticket_count})")

                            # Schedule with error handling for high concurrency
                            future = self._schedule_async_task(self._broadcast_update(), track_broadcast=True)
                            if future is None:
                                self.logger.warning(f"⚠️ MONITOR: Failed to schedule broadcast for task {self.task_id} - event loop unavailable")
                                broadcast_failures += 1
                            else:
                                self.last_update_time = current_time
                                last_successful_broadcast = current_time

                        except Exception as e:
                            self.logger.error(f"❌ MONITOR: Error scheduling broadcast for task {self.task_id}: {e}")
                            broadcast_failures += 1

                elif should_broadcast and self._should_stop_broadcasting():
                    # Log that we're skipping broadcast due to terminal state
                    self.logger.debug(f"🛑 MONITOR: Skipping broadcast for task {self.task_id} - task in terminal state (status={self.overall_status}, final_update_sent={self.final_update_sent})")

                # If task is fully completed, send final update and stop monitoring
                if is_completed and not self.final_update_sent:
                    self.logger.info(f"Task {self.task_id} fully completed - sending final update")
                    # ALWAYS send final completion update, regardless of broadcast timing
                    # This ensures frontend receives completion status
                    if not should_broadcast:
                        self.logger.info(f"📡 MONITOR: Force sending final completion update for task {self.task_id}")
                        self._schedule_async_task(self._broadcast_update(), track_broadcast=True)

                    # Schedule delayed final broadcast to guarantee delivery
                    self._schedule_delayed_final_broadcast_for_completion()
                    self.final_update_sent = True

                    # Sample final memory usage
                    self.memory_monitor.sample_memory("task_completed")
                    break
                elif is_completed and self.final_update_sent:
                    # Already sent final update, just break out of monitoring loop
                    self.logger.debug(f"Task {self.task_id} monitoring stopped")
                    break

                # If task has failed (all tasks in error state), send final error update and stop monitoring
                if self.overall_status == 'error' and not self.final_update_sent:
                    self.logger.info(f"Task {self.task_id} failed - sending final error update")
                    # ALWAYS send final error update to ensure frontend has error status
                    self.logger.info(f"📡 MONITOR: Force sending final error update for task {self.task_id}")
                    self._schedule_async_task(self._broadcast_update(), track_broadcast=True)

                    # Schedule delayed final broadcast to guarantee delivery
                    self._schedule_delayed_final_broadcast_for_error()
                    self.final_update_sent = True

                    # Sample final memory usage
                    self.memory_monitor.sample_memory("task_failed")
                    break
                elif self.overall_status == 'error' and self.final_update_sent:
                    # Already sent final error update, just break out of monitoring loop
                    self.logger.debug(f"Task {self.task_id} error monitoring stopped")
                    break

                # If overall status is error, stop all tasks
                if self.overall_status == 'error':
                    self.logger.info(f"Overall status is error, stopping all tasks for {self.task_id}")
                    self.stop_all_tasks()
                    break

                # Check for broadcast starvation in high concurrency scenarios
                if self.amount >= 500:
                    time_since_last_broadcast = current_time - last_successful_broadcast
                    if time_since_last_broadcast > broadcast_interval * 5:  # 5x the expected interval
                        self.logger.warning(f"⚠️ MONITOR: Broadcast starvation detected for task {self.task_id} - {time_since_last_broadcast:.1f}s since last successful broadcast")
                        # Force a broadcast attempt
                        if not self._should_stop_broadcasting():
                            self.logger.info(f"🔄 MONITOR: Forcing broadcast attempt for task {self.task_id}")
                            try:
                                future = self._schedule_async_task(self._broadcast_update(), track_broadcast=True)
                                if future:
                                    last_successful_broadcast = current_time
                                    self.last_update_time = current_time
                            except Exception as force_e:
                                self.logger.error(f"❌ MONITOR: Failed to force broadcast for task {self.task_id}: {force_e}")

                time.sleep(monitor_interval)

            except Exception as e:
                self.logger.error(f"Error in task monitoring for {self.task_id}: {e}", exc_info=True)
                # Continue monitoring despite errors
                time.sleep(monitor_interval * 2)
    
    def _calculate_overall_status(self) -> Tuple[str, Union[str, None]]:
        """
        Calculate overall status based on individual task states using >50% majority rule.

        This method aggregates the status of all individual Queue-IT tasks to determine
        the overall processing status. It implements the following logic:

        1. If no tasks exist, return 'initializing'
        2. If enough tasks completed (>= ticket_count), return 'completed'
        3. Find status that represents >50% of tasks, prioritized by importance
        4. Fall back to most common status if no majority exists

        Queue-IT Processing Stages (in priority order):
        - completed: Successfully obtained pass links (highest priority)
        - active: Actively processing in the queue
        - waiting: Waiting for queue progression
        - queued: Waiting in the queue system
        - solving: Working on queue bypass challenges
        - initializing: Setting up tasks
        - error: Failed due to errors (lowest priority)

        Returns:
            Tuple[str, Union[str, None]]: A tuple containing:
                - status (str): The calculated overall status
                - error_message (Union[str, None]): Error message if status is 'error', None otherwise

        Note:
            The >50% majority rule ensures that the overall status reflects the state
            of most tasks, providing accurate progress representation to users.
        """
        if not self.tasks:
            return "initializing", None

        # Count tasks in each status (optimized: iterate over dict values)
        status_counts = {}
        error_messages = []
        for task in self.tasks.values():
            encrypted_logger.debug(f"Identifying status for task {task['task_index']}: {task['status']}")
            status = task['status']
            status_counts[status] = status_counts.get(status, 0) + 1
            if status == 'error' and task.get('error'):
                # Sanitize error message to hide proxy credentials before adding to list
                sanitized_error = self._sanitize_error_message_for_display(task['error'])
                error_messages.append(sanitized_error)

        total_tasks = len(self.tasks)

        # Check if we have enough completed tasks
        completed_count = status_counts.get('completed', 0)
        if completed_count >= self.ticket_count:
            return 'completed', None

        # Define status priority (higher number = higher priority)
        # This determines which status takes precedence when multiple statuses exist
        status_priority = {
            'error': 0,          # Lowest priority - only shown if majority are errors
            'initializing': 1,   # Initial setup phase
            'solving': 2,        # Working on bypass challenges
            'queued': 3,         # Waiting in queue system
            'waiting': 4,        # Waiting for queue progression
            'active': 5,         # Actively processing
            'completed': 6       # Highest priority - successfully completed
        }

        # Find the status with >50% of tasks, prioritized by importance
        for status in sorted(status_priority.keys(), key=lambda x: status_priority[x], reverse=True):
            count = status_counts.get(status, 0)
            if count / total_tasks > 0.5:
                if status == 'error' and error_messages:
                    # Return most common error message for error status
                    from collections import Counter
                    error_counter = Counter(error_messages)
                    most_common = error_counter.most_common(1)
                    encrypted_logger.debug(f"Most common error message: {most_common[0][0]}")
                    return status, most_common[0][0] if most_common else error_messages[0]
                encrypted_logger.debug(f"Returning status: {status}")
                return status, None

        # Default to the most common status if no majority exists
        most_common_status = max(status_counts.items(), key=lambda x: x[1])[0] if status_counts else 'initializing'
        if most_common_status == 'error' and error_messages:
            # Return most common error message for error status
            from collections import Counter
            error_counter = Counter(error_messages)
            most_common = error_counter.most_common(1)
            encrypted_logger.debug(f"Most common error message: {most_common[0][0]}")
            return most_common_status, most_common[0][0] if most_common else error_messages[0]
        encrypted_logger.debug(f"Returning status: {most_common_status}")
        return most_common_status, None

    def _sanitize_error_message_for_display(self, error_msg: str) -> str:
        """
        Sanitize error message to hide proxy credentials before displaying to frontend.

        Args:
            error_msg: Original error message that may contain proxy credentials

        Returns:
            str: Sanitized error message safe for frontend display
        """
        try:
            if not error_msg or not self.proxies:
                return error_msg

            sanitized_msg = error_msg

            # Sanitize each proxy that might appear in the error message
            for proxy in self.proxies:
                if proxy and proxy in error_msg:
                    # Handle ip:port:user:pass format
                    if ':' in proxy:
                        parts = proxy.split(':')
                        if len(parts) == 4:
                            ip, port, username, password = parts
                            # Replace credentials in error message
                            sanitized_msg = sanitized_msg.replace(username, '***')
                            sanitized_msg = sanitized_msg.replace(password, '***')
                            # Replace full proxy string
                            sanitized_msg = sanitized_msg.replace(proxy, f"{ip}:{port}:***:***")
                        elif len(parts) == 2:
                            # ip:port format (no credentials to hide)
                            continue

                    # Handle URL format proxies (*******************:port)
                    elif '@' in proxy:
                        parts = proxy.split('@')
                        if len(parts) == 2:
                            ip_port = parts[1]
                            sanitized_msg = sanitized_msg.replace(proxy, f"***:***@{ip_port}")
                    else:
                        # Replace unknown proxy format with generic placeholder
                        sanitized_msg = sanitized_msg.replace(proxy, "***:***:***:***")

            # Additional cleanup for common proxy-related error patterns
            import re
            # Remove any remaining credential-like patterns (user:pass combinations)
            sanitized_msg = re.sub(r'[a-zA-Z0-9_-]+:[a-zA-Z0-9_-]{8,}', '***:***', sanitized_msg)

            return sanitized_msg

        except Exception as e:
            encrypted_logger.debug(f"Error sanitizing error message: {e}")
            # If sanitization fails, return a generic error message
            return "Task failed - connection error"

    def _is_task_fully_completed(self) -> bool:
        """
        Check if the task is fully completed with all required passlinks collected.

        A task is considered fully completed when:
        1. The overall status is 'completed'
        2. All required passlinks have been successfully collected (>= ticket_count)

        Returns:
            bool: True if task is fully completed, False otherwise
        """
        # Check if overall status is completed
        if self.overall_status != 'completed':
            return False

        # Check if we have collected all required passlinks
        if len(self.pass_links) < self.ticket_count:
            return False

        # Additional validation: ensure passlinks are valid (not empty)
        valid_passlinks = [link for link in self.pass_links if link and link.strip()]
        if len(valid_passlinks) < self.ticket_count:
            self.logger.warning(f"Task {self.task_id} has {len(self.pass_links)} passlinks but only {len(valid_passlinks)} are valid")
            return False

        return True

    def _should_stop_broadcasting(self):
        """
        Check if broadcasting should be stopped based on task state.

        Returns True if broadcasting should stop, False if it should continue.
        """
        # PRIORITY 1: Stop broadcasting immediately if stop event is set
        if self.stop_event.is_set():
            return True

        # PRIORITY 2: Stop broadcasting immediately if task is not running
        if not self.is_running:
            return True

        # PRIORITY 3: Stop broadcasting if overall status indicates termination
        # BUT allow one final broadcast for completion/error states to notify the client
        if self.overall_status == 'stopped':
            return True

        # PRIORITY 4: Stop broadcasting ONLY after final update has been sent for terminal states
        # This ensures completion/error updates always reach the frontend
        if self.final_update_sent and self.overall_status in ['completed', 'error', 'stopped']:
            return True

        # PRIORITY 5: Stop broadcasting if all tasks are in terminal states and we've reached completion
        # BUT only after final update has been sent
        if self._is_task_fully_completed() and self.final_update_sent:
            return True

        return False

    def _send_final_broadcast_before_stop(self):
        """
        Send final broadcast message before stopping tasks to ensure clients receive terminal status.
        This method guarantees that the final status (stopped/error/completed) is communicated
        to clients before the task manager shuts down.
        """
        try:
            self.logger.info(f"📡 FINAL BROADCAST: Sending final broadcast before stopping task {self.task_id}")

            # Mark all non-terminal tasks as stopped for cleanup, but preserve completed/error tasks
            current_time = time.time()
            for task in self.tasks.values():
                if task['status'] not in ['completed', 'error']:
                    task['status'] = 'stopped'
                    task['error'] = 'Task stopped by user'
                    task['last_status_change'] = current_time

            # Recalculate overall status after marking non-terminal tasks as stopped
            # This will preserve completed/error status if enough tasks achieved those states
            self.overall_status, self.error_message = self._calculate_overall_status()

            # Send immediate final broadcast
            if self.websocket_server:
                try:
                    # Try to schedule immediate final broadcast using existing event loop
                    import asyncio
                    try:
                        # Check if we're in an event loop
                        loop = asyncio.get_running_loop()
                        # Schedule the broadcast in the existing loop
                        asyncio.create_task(self._broadcast_final_status())
                        self.logger.info(f"📡 FINAL BROADCAST: Scheduled immediate final broadcast for task {self.task_id}")
                    except RuntimeError:
                        # No running loop, create a new one
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        try:
                            loop.run_until_complete(self._broadcast_final_status())
                            self.logger.info(f"📡 FINAL BROADCAST: Successfully sent final broadcast for task {self.task_id}")
                        finally:
                            loop.close()

                    # Schedule a delayed final broadcast as backup (2 seconds later)
                    self._schedule_delayed_final_broadcast()

                except Exception as broadcast_error:
                    self.logger.error(f"📡 FINAL BROADCAST: Failed to send immediate final broadcast for task {self.task_id}: {broadcast_error}")
                    # Still schedule delayed broadcast as fallback
                    self._schedule_delayed_final_broadcast()
            else:
                self.logger.warning(f"📡 FINAL BROADCAST: No WebSocket server available for final broadcast of task {self.task_id}")

        except Exception as e:
            self.logger.error(f"📡 FINAL BROADCAST: Error in final broadcast preparation for task {self.task_id}: {e}")

    def _schedule_delayed_final_broadcast(self):
        """
        Schedule a delayed final broadcast as a backup to ensure the message is delivered.
        This runs 2 seconds after the immediate broadcast to guarantee delivery.
        """
        try:
            import threading
            import time

            def delayed_broadcast():
                try:
                    time.sleep(2.0)  # Wait 2 seconds

                    if self.websocket_server:
                        self.logger.info(f"📡 DELAYED FINAL BROADCAST: Sending delayed final broadcast for task {self.task_id}")

                        # Create a new event loop for the delayed broadcast
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        try:
                            loop.run_until_complete(self._broadcast_final_status())
                            self.logger.info(f"📡 DELAYED FINAL BROADCAST: Successfully sent delayed final broadcast for task {self.task_id}")
                        finally:
                            loop.close()

                except Exception as delayed_error:
                    self.logger.error(f"📡 DELAYED FINAL BROADCAST: Failed to send delayed final broadcast for task {self.task_id}: {delayed_error}")

            # Start the delayed broadcast thread
            delayed_thread = threading.Thread(target=delayed_broadcast, daemon=True)
            delayed_thread.start()
            self.logger.info(f"📡 DELAYED FINAL BROADCAST: Scheduled delayed final broadcast for task {self.task_id} in 2 seconds")

        except Exception as e:
            self.logger.error(f"📡 DELAYED FINAL BROADCAST: Error scheduling delayed final broadcast for task {self.task_id}: {e}")

    def _schedule_delayed_final_broadcast_for_completion(self):
        """Schedule delayed final broadcast for completion state."""
        self._schedule_delayed_final_broadcast_with_status('completed')

    def _schedule_delayed_final_broadcast_for_error(self):
        """Schedule delayed final broadcast for error state."""
        self._schedule_delayed_final_broadcast_with_status('error')

    def _schedule_delayed_final_broadcast_with_status(self, final_status):
        """
        Schedule a delayed final broadcast with specific status.
        This ensures the final status is delivered even if immediate broadcast fails.
        """
        try:
            import threading
            import time

            def delayed_broadcast():
                try:
                    time.sleep(2.0)  # Wait 2 seconds

                    if self.websocket_server:
                        self.logger.info(f"📡 DELAYED FINAL BROADCAST: Sending delayed final broadcast for task {self.task_id} with status {final_status}")

                        # Temporarily set the status for final broadcast
                        original_status = self.overall_status
                        self.overall_status = final_status

                        # Create a new event loop for the delayed broadcast
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        try:
                            loop.run_until_complete(self._broadcast_final_status())
                            self.logger.info(f"📡 DELAYED FINAL BROADCAST: Successfully sent delayed final broadcast for task {self.task_id} with status {final_status}")
                        finally:
                            # Restore original status
                            self.overall_status = original_status
                            loop.close()

                except Exception as delayed_error:
                    self.logger.error(f"📡 DELAYED FINAL BROADCAST: Failed to send delayed final broadcast for task {self.task_id} with status {final_status}: {delayed_error}")

            # Start the delayed broadcast thread
            delayed_thread = threading.Thread(target=delayed_broadcast, daemon=True)
            delayed_thread.start()
            self.logger.info(f"📡 DELAYED FINAL BROADCAST: Scheduled delayed final broadcast for task {self.task_id} with status {final_status} in 2 seconds")

        except Exception as e:
            self.logger.error(f"📡 DELAYED FINAL BROADCAST: Error scheduling delayed final broadcast for task {self.task_id} with status {final_status}: {e}")

    async def _broadcast_final_status(self):
        """
        Broadcast the final status message to clients.
        This is a specialized broadcast method for terminal states.
        """
        try:
            if not self.websocket_server:
                return

            # Prepare final status update
            update_data = {
                'type': 'queue_progress_update',
                'task_id': self.task_id,
                'timestamp': time.time(),
                'overall_status': self.overall_status,
                'error_message': getattr(self, 'error_message', None),
                'ticket_count': self.ticket_count,
                'pass_links_count': len(self.pass_links),
                'pass_links': self.pass_links,
                'duration': time.time() - self.start_time if self.start_time else 0,
                'is_final_update': True,  # Always mark as final for terminal states
                'completion_time': getattr(self, 'completion_time', None),
                'final_broadcast': True  # Special flag to indicate this is the guaranteed final broadcast
            }

            self.logger.debug(f"📡 FINAL STATUS BROADCAST: Broadcasting final status for task {self.task_id}: {self.overall_status}")

            # Send the final broadcast
            await self.websocket_server.broadcast_to_client(self.client_id, update_data)

            # Mark final update as sent
            self.final_update_sent = True

        except Exception as e:
            self.logger.error(f"📡 FINAL STATUS BROADCAST: Failed to broadcast final status for task {self.task_id}: {e}")

    async def _broadcast_update(self):
        """
        Broadcast current progress to WebSocket clients.

        This method checks if broadcasting should continue based on task state
        and stops broadcasting when tasks reach terminal states.
        """
        if not self.websocket_server:
            self.logger.debug("No websocket server available for broadcast")
            return

        # Check if we should stop broadcasting
        if self._should_stop_broadcasting():
            self.logger.debug(f"Stopping broadcast for task {self.task_id} - task in terminal state")
            return

        try:
            # Prepare task summary (optimized: iterate over dict values)
            task_summary = []
            for task in self.tasks.values():
                task_summary.append({
                    'task_index': task['task_index'],
                    'status': task['status'],
                    'error': task['error'],
                    'duration': time.time() - task['start_time']
                })

            # Check if this is a completion update
            is_fully_completed = self._is_task_fully_completed()

            # Prepare update message
            update_data = {
                'type': 'queue_progress_update',
                'task_id': self.task_id,
                'timestamp': time.time(),
                'overall_status': self.overall_status,
                'error_message': self.error_message if hasattr(self, 'error_message') else None,
                'ticket_count': self.ticket_count,
                'pass_links_count': len(self.pass_links),
                'pass_links': self.pass_links,
                'duration': time.time() - self.start_time if self.start_time else 0,
                'is_final_update': is_fully_completed,  # Signal to frontend that no more updates will come
                'completion_time': getattr(self, 'completion_time', None)
            }

            # Log different messages based on completion status
            if is_fully_completed:
                self.logger.info(f"Broadcasting FINAL update for task {self.task_id}: status={self.overall_status}, pass_links={len(self.pass_links)}/{self.ticket_count}")
            else:
                self.logger.debug(f"Broadcasting update for task {self.task_id}: status={self.overall_status}, pass_links={len(self.pass_links)}/{self.ticket_count}")

            # Enhanced debugging for broadcast
            self.logger.debug(f"📡 TASK BROADCAST: Attempting to broadcast to client_id={self.client_id}, task_id={self.task_id}")
            self.logger.debug(f"📡 TASK BROADCAST: Update data type={update_data.get('type')}, overall_status={update_data.get('overall_status')}")

            # Broadcast to WebSocket server
            await self.websocket_server.broadcast_to_client(self.client_id, update_data)

            self.logger.debug(f"✅ TASK BROADCAST: Broadcast call completed for task {self.task_id}")

        except Exception as e:
            self.logger.error(f"Failed to broadcast update for task {self.task_id}: {e}", exc_info=True)
    
    async def _complete_all_tasks(self):
        """Complete the task manager when enough pass-links are obtained"""
        self.overall_status = 'completed'
        self.completion_time = time.time()

        self.logger.info(f"Task manager {self.task_id} completed successfully with {len(self.pass_links)} passlinks")

        # Mark all tasks as completed since we reached the target (optimized: iterate over dict values)
        for task in self.tasks.values():
            if task['status'] not in ['completed', 'error', 'stopped']:
                task['status'] = 'completed'
                task['last_status_change'] = time.time()
                # Stop the individual handler
                if task.get('handler'):
                    task['handler'].stop()

        # Send completion webhook
        await self._send_completion_webhook()

        # Send final completion update to frontend
        await self._broadcast_update()

        # Schedule delayed final broadcast to guarantee delivery
        self._schedule_delayed_final_broadcast_for_completion()
        self.final_update_sent = True

        # Mark as not running to stop the monitoring thread
        # Note: The monitoring thread will detect completion via _is_task_fully_completed()
        # and stop sending further updates, but we set this to ensure cleanup
        self.is_running = False

        # Stop all remaining tasks (synchronous method)
        self.stop_all_tasks()

# Create global singleton instance
# This global instance ensures consistent task management across the application
queue_task_manager = QueueTaskManager()
