#!/bin/bash

# =============================================================================
# SIMPLE BUILD SCRIPT FOR KASPER-Q (NO NOTARIZATION)
# =============================================================================
# This script builds the app without notarization for testing purposes
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}🔹 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  $1"
    echo "=============================================="
    echo -e "${NC}"
}

# Function to create backend structure and copy backend app
create_backend_structure() {
    print_step "Creating backend directory and copying backend app..."

    # Create backend directory
    mkdir -p backend

    # Define backend source path
    BACKEND_SOURCE="../kasper-q-backend/dist/Kasper-Q.app"

    # Check if backend app exists
    if [ ! -d "$BACKEND_SOURCE" ]; then
        print_error "Backend app not found at: $BACKEND_SOURCE"
        print_warning "Please ensure the backend project is built first"
        print_warning "You can build it by running:"
        print_warning "  cd ../kasper-q-backend"
        print_warning "  python build_menubar.py"
        exit 1
    fi

    print_step "Copying backend app from: $BACKEND_SOURCE"
    cp -R "$BACKEND_SOURCE" backend/

    # Create start_backend.sh script
    cat > backend/start_backend.sh << 'EOF'
#!/bin/bash

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if backend executable exists
BACKEND_EXECUTABLE="$SCRIPT_DIR/Kasper-Q.app/Contents/MacOS/kasper_q_menubar"

if [ -f "$BACKEND_EXECUTABLE" ]; then
    echo "Starting backend executable: $BACKEND_EXECUTABLE"
    # Make sure the executable has proper permissions
    chmod +x "$BACKEND_EXECUTABLE"
    # Start the backend executable directly
    exec "$BACKEND_EXECUTABLE"
else
    echo "Backend executable not found at: $BACKEND_EXECUTABLE"
    echo "Please ensure the backend app is properly copied"
    exit 1
fi
EOF

    # Make the script executable
    chmod +x backend/start_backend.sh

    print_success "Backend structure created with Kasper-Q.app"
    print_success "Backend app copied to: backend/Kasper-Q.app"
    print_success "Start script created: backend/start_backend.sh"
}

# Main execution
main() {
    print_header "SIMPLE BUILD FOR KASPER-Q"
    echo "This script will build the app without notarization for testing"
    echo ""
    
    print_step "Cleaning previous build..."
    rm -rf dist build
    
    print_step "Installing dependencies..."
    npm install
    
    print_step "Building React app..."
    NODE_OPTIONS="--max-old-space-size=2048" npm run build
    
    # Create backend structure before building Electron app
    create_backend_structure
    
    print_step "Building Electron app..."
    NODE_OPTIONS="--max-old-space-size=2048" npm run build:mac
    
    if [ ! -d "dist/mac/Kasper-Q.app" ]; then
        print_error "App build failed - app not found"
        exit 1
    fi
    
    print_success "Build completed successfully"
    print_step "App location: dist/mac/Kasper-Q.app"
    
    # Check backend structure
    print_step "Checking backend structure..."
    ./check-backend-structure.sh
    
    echo ""
    echo -e "${GREEN}🎉 BUILD COMPLETE!${NC}"
    echo "You can now test the app at: dist/mac/Kasper-Q.app"
    echo ""
    echo -e "${YELLOW}⚠️  To view debug logs:${NC}"
    echo "1. Open Console.app (Applications > Utilities > Console)"
    echo "2. Filter by 'KASPER-Q' to see debug messages"
    echo "3. Run the app and check for backend startup messages"
}

# Run main function
main "$@"
