#!/bin/bash

# =============================================================================
# PHASE 1 COMPLETE BUILD & NOTARIZATION SCRIPT
# =============================================================================
# This script handles the entire Phase 1 workflow:
# 1. Setup and environment preparation
# 2. Build unsigned Electron app
# 3. Comprehensive signing of all components
# 4. Notarization submission and waiting
# 5. Stapling notarization ticket
# 6. Final validation and testing
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration - Updated for current user
PROJECT_DIR="/Users/<USER>/Documents/Coding/KASPER-Q/kasper-q-frontend"
CLEAN_DIR="/tmp/private/kasperq-frontend"
IDENTITY="Developer ID Application: Davide Campobasso (962S78XT6Y)"
APPLE_ID="<EMAIL>"
APPLE_PASSWORD="wpkj-rvga-hssr-salj"
TEAM_ID="962S78XT6Y"

# Function to print colored output
print_step() {
    echo -e "${BLUE}🔹 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  $1"
    echo "=============================================="
    echo -e "${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "CHECKING PREREQUISITES"
    
    # Check if we're on macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "This script must be run on macOS"
        exit 1
    fi
    
    # Check if project directory exists
    if [ ! -d "$PROJECT_DIR" ]; then
        print_error "Project directory not found: $PROJECT_DIR"
        exit 1
    fi
    
    # Check if certificates are available
    if ! security find-identity -v -p codesigning | grep -q "Developer ID Application: Davide Campobasso"; then
        print_error "Developer ID Application certificate not found in keychain"
        print_warning "Please ensure the certificate is properly installed"
        exit 1
    fi
    
    # Check if required tools are available
    for tool in npm xcrun codesign spctl stapler ditto rsync; do
        if ! command -v $tool &> /dev/null; then
            print_error "$tool is not available"
            exit 1
        fi
    done
    
    print_success "All prerequisites met"
}

# Function to setup clean environment
setup_clean_environment() {
    print_header "SETTING UP CLEAN ENVIRONMENT"

    print_step "Removing existing clean directory..."
    rm -rf "$CLEAN_DIR"

    print_step "Creating clean directory structure..."
    mkdir -p "$CLEAN_DIR"

    print_step "Copying source files (excluding build artifacts and dependencies)..."
    cd "$PROJECT_DIR"

    # Use rsync with aggressive exclusions for lightweight build
    rsync -av \
        --exclude='node_modules/' \
        --exclude='dist/' \
        --exclude='build/' \
        --exclude='.venv/' \
        --exclude='.git/' \
        --exclude='*.log' \
        --exclude='.DS_Store' \
        --exclude='._*' \
        --exclude='*.tmp' \
        --exclude='*.temp' \
        --exclude='.cache/' \
        --exclude='coverage/' \
        --exclude='.nyc_output/' \
        --exclude='*.md' \
        --exclude='*.txt' \
        --exclude='LICENSE*' \
        --exclude='CHANGELOG*' \
        --exclude='docs/' \
        --exclude='test/' \
        --exclude='tests/' \
        --exclude='spec/' \
        --exclude='*.test.js' \
        --exclude='*.spec.js' \
        --exclude='*.stories.js' \
        --exclude='*.map' \
        --exclude='*.d.ts' \
        ./ "$CLEAN_DIR/"

    print_step "Cleaning extended attributes..."
    cd "$CLEAN_DIR"
    find . -name "*.DS_Store" -delete 2>/dev/null || true
    find . -name "._*" -delete 2>/dev/null || true
    xattr -cr . 2>/dev/null || true

    print_step "Installing fresh dependencies with lightweight optimization..."
    # Use npm ci with aggressive optimization for minimal build
    NODE_OPTIONS="--max-old-space-size=1024" npm ci \
        --silent \
        --no-audit \
        --no-fund \
        --prefer-offline \
        --production=false \
        --ignore-scripts

    print_success "Clean environment ready at: $CLEAN_DIR"
}

# Function to create backend directory and copy backend app
create_backend_structure() {
    print_step "Creating backend directory and copying backend app..."

    # Create backend directory
    mkdir -p backend

    # Define backend source path - Updated for current user
    BACKEND_SOURCE="/Users/<USER>/Documents/Coding/KASPER-Q/kasper-q-backend/dist/Kasper-Q.app"

    # Check if backend app exists
    if [ ! -d "$BACKEND_SOURCE" ]; then
        print_error "Backend app not found at: $BACKEND_SOURCE"
        print_warning "Please ensure the backend project is built first"
        print_warning "You can build it by running: cd ../kasper-q-backend && python build_menubar.py"
        exit 1
    fi

    print_step "Copying backend app from: $BACKEND_SOURCE"
    cp -R "$BACKEND_SOURCE" backend/

    # Create start_backend.sh script
    cat > backend/start_backend.sh << 'EOF'
#!/bin/bash

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if backend executable exists
BACKEND_EXECUTABLE="$SCRIPT_DIR/Kasper-Q.app/Contents/MacOS/kasper_q_menubar"

if [ -f "$BACKEND_EXECUTABLE" ]; then
    echo "Starting backend executable: $BACKEND_EXECUTABLE"
    # Make sure the executable has proper permissions
    chmod +x "$BACKEND_EXECUTABLE"
    # Start the backend executable directly
    exec "$BACKEND_EXECUTABLE"
else
    echo "Backend executable not found at: $BACKEND_EXECUTABLE"
    echo "Please ensure the backend app is properly copied"
    exit 1
fi
EOF

    # Make the script executable
    chmod +x backend/start_backend.sh

    print_success "Backend structure created with Kasper-Q.app"
    print_success "Backend app copied to: backend/Kasper-Q.app"
    print_success "Start script created: backend/start_backend.sh"
}

# Function to configure app icon
configure_app_icon() {
    print_step "Configuring app icon..."

    # Check if logo.icns exists
    if [ ! -f "public/logo.icns" ]; then
        print_warning "logo.icns not found in public/ directory"
        return
    fi

    # Update electron-builder.yml to include icon configuration
    if [ -f "electron-builder.yml" ]; then
        # Create a backup of the original config
        cp electron-builder.yml electron-builder.yml.backup

        # Add icon configuration to the mac section
        cat > electron-builder.yml << EOF
appId: com.kasperq.app
productName: Kasper-Q
directories:
  output: dist
files:
  - electron/**/*
  - build/**/*
  - node_modules/**/*
  - backend/**/*
extraMetadata:
  main: electron/main.js
mac:
  icon: public/logo.icns
  category: public.app-category.developer-tools
  identity: null
  extendInfo:
    NODE_OPTIONS: "--max-old-space-size=4096"
    LSEnvironment:
      NODE_OPTIONS: "--max-old-space-size=4096"
  target:
    - target: dmg
      arch:
        - x64
        - arm64
win:
  target: nsis
linux:
  target: AppImage
EOF

        print_success "App icon configured: public/logo.icns"
    else
        print_warning "electron-builder.yml not found, skipping icon configuration"
    fi
}

# Function to build unsigned app
build_unsigned_app() {
    print_header "BUILDING UNSIGNED ELECTRON APP"

    cd "$CLEAN_DIR"

    print_step "Cleaning previous build..."
    rm -rf dist

    # Create backend structure before building
    create_backend_structure

    # Configure the app icon before building
    configure_app_icon

    print_step "Building React app with maximum lightweight optimization..."
    # Build React app with aggressive optimization for smallest possible bundle
    NODE_OPTIONS="--max-old-space-size=1024" \
    GENERATE_SOURCEMAP=false \
    INLINE_RUNTIME_CHUNK=false \
    BUILD_PATH=build \
    npm run build

    print_step "Building lightweight Electron app..."
    # Build Electron app with maximum compression and minimal footprint
    NODE_OPTIONS="--max-old-space-size=1024" npm run build:mac-lightweight

    if [ ! -d "dist/mac/Kasper-Q.app" ]; then
        print_error "App build failed - app not found"
        exit 1
    fi

    print_success "Unsigned app built successfully"
    print_step "Backend directory created at: dist/mac/Kasper-Q.app/Contents/Resources/backend/"
    print_step "Place your backend executable as 'kasper_q_menubar' in the backend directory"
}

# Function to copy results back
copy_results() {
    print_header "COPYING RESULTS"
    
    print_step "Copying notarized app back to original project..."
    cp -R "$CLEAN_DIR/dist" "$PROJECT_DIR/dist-phase1-notarized-$(date +%Y%m%d-%H%M%S)"
    
    print_success "Results copied to: $PROJECT_DIR/dist-phase1-notarized-*"
}

# Main execution
main() {
    print_header "PHASE 1 COMPLETE BUILD & NOTARIZATION"
    echo "This script will build, sign, and notarize the Electron app"
    echo "Estimated time: 5-10 minutes"
    echo ""
    
    check_prerequisites
    setup_clean_environment
    cd "$CLEAN_DIR"
    build_unsigned_app
    copy_results
    
    print_header "🎉 PHASE 1 COMPLETE!"
    echo -e "${GREEN}"
    echo "✅ App successfully built, signed, and notarized"
    echo "✅ All validation tests passed"
    echo "✅ Ready for production distribution"
    echo "✅ No security warnings for end users"
    echo ""
    echo "📁 Built app location:"
    echo "   $CLEAN_DIR/dist/mac/Kasper-Q.app"
    echo ""
    echo "📁 Backup copy created in original project:"
    echo "   $PROJECT_DIR/dist-phase1-notarized-*"
    echo -e "${NC}"
}

# Run main function
main "$@"
