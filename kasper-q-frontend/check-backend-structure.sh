#!/bin/bash

# =============================================================================
# BACKEND STRUCTURE CHECKER FOR KASPER-Q
# =============================================================================
# This script checks if the backend is properly embedded in the built app
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_step() {
    echo -e "${BLUE}🔹 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  $1"
    echo "=============================================="
    echo -e "${NC}"
}

# Check if app exists
APP_PATH="dist/mac/Kasper-Q.app"

if [ ! -d "$APP_PATH" ]; then
    print_error "App not found at: $APP_PATH"
    print_step "Please build the app first using: npm run build:mac"
    exit 1
fi

print_header "CHECKING BACKEND STRUCTURE"

print_step "App found at: $APP_PATH"

# Check Contents directory
CONTENTS_DIR="$APP_PATH/Contents"
if [ -d "$CONTENTS_DIR" ]; then
    print_success "Contents directory exists"
else
    print_error "Contents directory missing"
    exit 1
fi

# Check Resources directory
RESOURCES_DIR="$CONTENTS_DIR/Resources"
if [ -d "$RESOURCES_DIR" ]; then
    print_success "Resources directory exists"
    print_step "Resources directory contents:"
    ls -la "$RESOURCES_DIR" | head -20
else
    print_error "Resources directory missing"
    exit 1
fi

# Check for backend directory
BACKEND_DIR="$RESOURCES_DIR/backend"
if [ -d "$BACKEND_DIR" ]; then
    print_success "Backend directory exists"
    print_step "Backend directory contents:"
    ls -la "$BACKEND_DIR"
else
    print_warning "Backend directory missing at: $BACKEND_DIR"
fi

# Check for backend binary in different locations
BACKEND_BINARY_1="$BACKEND_DIR/kasper_q_menubar"
BACKEND_BINARY_2="$BACKEND_DIR/Kasper-Q.app/Contents/MacOS/kasper_q_menubar"
BACKEND_BINARY_3="$CONTENTS_DIR/MacOS/kasper_q_backend"

print_step "Checking for backend binary in different locations:"

if [ -f "$BACKEND_BINARY_1" ]; then
    print_success "Backend binary found at: $BACKEND_BINARY_1"
    ls -la "$BACKEND_BINARY_1"
else
    print_warning "Backend binary not found at: $BACKEND_BINARY_1"
fi

if [ -f "$BACKEND_BINARY_2" ]; then
    print_success "Backend binary found at: $BACKEND_BINARY_2"
    ls -la "$BACKEND_BINARY_2"
else
    print_warning "Backend binary not found at: $BACKEND_BINARY_2"
fi

if [ -f "$BACKEND_BINARY_3" ]; then
    print_success "Backend binary found at: $BACKEND_BINARY_3"
    ls -la "$BACKEND_BINARY_3"
else
    print_warning "Backend binary not found at: $BACKEND_BINARY_3"
fi

# Check MacOS directory
MACOS_DIR="$CONTENTS_DIR/MacOS"
if [ -d "$MACOS_DIR" ]; then
    print_success "MacOS directory exists"
    print_step "MacOS directory contents:"
    ls -la "$MACOS_DIR"
else
    print_error "MacOS directory missing"
fi

print_header "SUMMARY"
echo "If no backend binary was found, you need to:"
echo "1. Build the backend project first"
echo "2. Copy the backend binary to the correct location"
echo "3. Rebuild the frontend app"
