#!/bin/bash

# =============================================================================
# DEBUG BUILD SCRIPT FOR KASPER-Q
# =============================================================================
# This script builds the app with debug logging enabled to diagnose
# backend startup issues after installation
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}🔹 $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_header() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "  $1"
    echo "=============================================="
    echo -e "${NC}"
}

# Main execution
main() {
    print_header "DEBUG BUILD FOR KASPER-Q"
    echo "This script will build the app with debug logging enabled"
    echo ""
    
    print_step "Cleaning previous build..."
    rm -rf dist build
    
    print_step "Building React app..."
    NODE_OPTIONS="--max-old-space-size=2048" npm run build
    
    print_step "Building Electron app with debug logging..."
    NODE_OPTIONS="--max-old-space-size=2048" npm run build:mac
    
    if [ ! -d "dist/mac/Kasper-Q.app" ]; then
        print_error "App build failed - app not found"
        exit 1
    fi
    
    print_success "Debug build completed successfully"
    print_step "App location: dist/mac/Kasper-Q.app"
    print_step "You can now test the app and check Console.app for debug logs"
    
    echo ""
    echo -e "${YELLOW}⚠️  To view debug logs:${NC}"
    echo "1. Open Console.app (Applications > Utilities > Console)"
    echo "2. Filter by 'KASPER-Q' to see debug messages"
    echo "3. Run the app and check for backend startup messages"
}

# Run main function
main "$@"
