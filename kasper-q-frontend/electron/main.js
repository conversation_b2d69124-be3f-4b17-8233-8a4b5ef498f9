const { app, BrowserWindow, ipcMain, shell } = require('electron')
const { spawn } = require('child_process')
const path = require('path');
const http = require('http');
const fs = require('fs');
const url = require('url');

// Simple and reliable way to detect if we're in development mode
// In packaged apps, app.isPackaged is true and __dirname will be inside app.asar
const isDev = !app.isPackaged;

/**
 * Safe console logging that prevents write EIO errors during shutdown
 * @param {string} message - The message to log
 * @param {string} level - Log level ('log', 'error', 'warn')
 */
function safeLog(message, level = 'log') {
  // Enable logging for debugging backend startup issues
  try {
    console[level](`[KASPER-Q] ${message}`);
  } catch (error) {
    // Ignore logging errors during shutdown
  }
}

// Keep a global reference to prevent garbage collection
let mainWindow;
let backendProcess = null;
let httpServer = null;
let reactDevProcess = null;

// Global error handlers to prevent crashes
process.on('uncaughtException', () => {
  // Error handling disabled for production performance
  // Don't exit on uncaught exceptions during shutdown
});

process.on('unhandledRejection', () => {
  // Error handling disabled for production performance
});

// Add startup timeout to prevent infinite loops
let startupComplete = false;
setTimeout(() => {
  if (!startupComplete) {
    // safeLog('Startup timeout reached - forcing app quit to prevent infinite loops', 'error');
    app.quit();
  }
}, 30000); // 30 second timeout

/**
 * Starts the React development server
 */
function startReactDevServer() {
  // console.log('Starting React development server...');
  
  const reactProcess = spawn('npm', ['start'], {
    cwd: path.join(__dirname, '..'),
    stdio: ['pipe', 'pipe', 'pipe'],
    env: { ...process.env, BROWSER: 'none', PORT: '3000' }
  });

  reactProcess.stdout.on('data', (data) => {
    const output = data.toString();
    // console.log('React dev server stdout:', output);
    
    // Check if server is ready
    if (output.includes('Local:') || output.includes('On Your Network:')) {
      // console.log('React dev server is ready!');
    }
  });

  reactProcess.stderr.on('data', (data) => {
    const output = data.toString();
    // console.log('React dev server stderr:', output);
    
    // Handle port already in use
    if (output.includes('Something is already running on port 3000')) {
      // console.log('Port 3000 is in use, trying to kill existing process...');
      // Try to kill the process on port 3000
      spawn('lsof', ['-ti:3000'], { stdio: 'pipe' })
        .on('close', (code) => {
          if (code === 0) {
            spawn('kill', ['-9', '$(lsof -ti:3000)'], { shell: true });
          }
        });
    }
  });

  reactProcess.on('close', () => {
    // Process cleanup - logging disabled for performance
  });

  reactProcess.on('error', () => {
    // Error handling disabled for performance
  });

  return reactProcess;
}

/**
 * Starts a simple HTTP server to serve the built React app
 */
function startHttpServer() {
  // console.log('Starting HTTP server for built React app...');

  if (httpServer) {
    // console.log('HTTP server already running');
    return;
  }

  // Add safety check to prevent multiple calls
  if (global.httpServerStarting) {
    // console.log('HTTP server startup already in progress');
    return;
  }
  global.httpServerStarting = true;

  // Determine the correct build path based on whether we're in a packaged app
  const isPackaged = !isDev;
  let buildPath;

  if (isPackaged) {
    // In packaged app, build files are inside the app.asar
    buildPath = path.join(__dirname, '../build');
    // console.log('Packaged app detected, using build path:', buildPath);
  } else {
    // In development, build files are in the project directory
    buildPath = path.join(__dirname, '../build');
    // console.log('Development mode detected, using build path:', buildPath);
  }

  // Check if build directory exists
  if (!fs.existsSync(buildPath)) {
    // console.error('Build directory not found:', buildPath);
    // console.error('Available files in __dirname:', fs.readdirSync(__dirname));
    // console.error('Available files in parent:', fs.readdirSync(path.join(__dirname, '..')));
    // console.error('Exiting due to missing build directory');
    app.quit();
    return;
  }

  // console.log('Build directory found:', buildPath);
  // console.log('Build directory contents:', fs.readdirSync(buildPath));
  
  httpServer = http.createServer((req, res) => {
    let filePath = url.parse(req.url).pathname;
    
    // Default to index.html for root path
    if (filePath === '/') {
      filePath = '/index.html';
    }
    
    // Remove leading slash for file system
    const fullPath = path.join(buildPath, filePath);
    
    // Check if file exists
    fs.access(fullPath, fs.constants.F_OK, (err) => {
      if (err) {
        // File not found, serve index.html for SPA routing
        const indexPath = path.join(buildPath, 'index.html');
        fs.readFile(indexPath, (err, data) => {
          if (err) {
            res.writeHead(404);
            res.end('File not found');
          } else {
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(data);
          }
        });
      } else {
        // File exists, serve it
        const ext = path.extname(fullPath);
        const contentType = {
          '.html': 'text/html',
          '.js': 'text/javascript',
          '.css': 'text/css',
          '.json': 'application/json',
          '.png': 'image/png',
          '.jpg': 'image/jpg',
          '.gif': 'image/gif',
          '.svg': 'image/svg+xml',
          '.ico': 'image/x-icon'
        }[ext] || 'text/plain';

        fs.readFile(fullPath, (err, data) => {
          if (err) {
            res.writeHead(500);
            res.end('Error loading file');
          } else {
            res.writeHead(200, { 'Content-Type': contentType });
            res.end(data);
          }
        });
      }
    });
  });

  httpServer.listen(1109, () => {
    // console.log('HTTP server started on port 1109');
    global.httpServerStarting = false;
  });

  httpServer.on('error', () => {
    // Error handling disabled for performance
    global.httpServerStarting = false;
  });
}

/**
 * Stops the HTTP server
 */
function stopHttpServer() {
  if (httpServer) {
    try {
      httpServer.close();
      httpServer = null;
      // safeLog('HTTP server stopped');
    } catch (error) {
      // safeLog(`Error stopping HTTP server: ${error.message}`, 'error');
    }
  }
}

/**
 * Stops the React development server
 */
function stopReactDevServer() {
  if (reactDevProcess) {
    try {
      // safeLog('Stopping React development server...');
      reactDevProcess.kill('SIGTERM');
      reactDevProcess = null;
      // safeLog('React development server stopped');
    } catch (error) {
      // safeLog(`Error stopping React development server: ${error.message}`, 'error');
    }
  }
}

/**
 * Starts the backend server by executing the binary directly
 */
function startBackendServer() {
  if (backendProcess) {
    safeLog('Backend server already running');
    return;
  }

  // In development mode, use the relative path
  // In production mode, use the absolute path to the Resources directory
  const isPackaged = !isDev;
  const backendDir = isPackaged
    ? path.join(process.resourcesPath, 'backend')
    : path.join(__dirname, '../Contents/Resources/backend');

  safeLog(`Starting backend server - isPackaged: ${isPackaged}`);
  safeLog(`Backend directory: ${backendDir}`);
  safeLog(`Process resources path: ${process.resourcesPath}`);

  // Try different backend locations
  const originalBackendPath = '/private/tmp/kasper-q-backend/dist/Kasper-Q.app/Contents/MacOS/kasper_q_menubar';
  const embeddedBackendPath = path.join(backendDir, 'kasper_q_menubar');
  const legacyFlattenedPath = path.join(process.resourcesPath, '..', 'MacOS', 'kasper_q_backend');

  safeLog('Checking embedded backend path:', embeddedBackendPath);
  safeLog('Embedded backend exists:', fs.existsSync(embeddedBackendPath));

  // Try paths in order of preference
  let binaryPath;
  safeLog(`Checking paths for backend binary:`);
  safeLog(`- Embedded: ${embeddedBackendPath} (exists: ${fs.existsSync(embeddedBackendPath)})`);
  safeLog(`- Original: ${originalBackendPath} (exists: ${fs.existsSync(originalBackendPath)})`);
  safeLog(`- Legacy: ${legacyFlattenedPath} (exists: ${fs.existsSync(legacyFlattenedPath)})`);

  if (fs.existsSync(embeddedBackendPath)) {
    binaryPath = embeddedBackendPath;
    safeLog('Using embedded backend');
  } else if (fs.existsSync(originalBackendPath)) {
    binaryPath = originalBackendPath;
    safeLog('Using original backend (fallback)');
  } else if (fs.existsSync(legacyFlattenedPath)) {
    binaryPath = legacyFlattenedPath;
    safeLog('Using legacy flattened backend');
  } else {
    safeLog('No backend binary found', 'error');
    safeLog('Checked paths:', 'error');
    safeLog(`- Embedded: ${embeddedBackendPath}`, 'error');
    safeLog(`- Original: ${originalBackendPath}`, 'error');
    safeLog(`- Legacy: ${legacyFlattenedPath}`, 'error');
    return;
  }

  safeLog('Starting backend server...');
  safeLog('Backend directory:', backendDir);
  safeLog('Binary path:', binaryPath);

  // Check if the binary exists
  if (!fs.existsSync(binaryPath)) {
    safeLog('Backend binary not found at: ' + binaryPath, 'error');
    safeLog('Please ensure the backend app is properly built and placed in the backend directory', 'error');
    return;
  }

  // Make sure the binary is executable
  try {
    fs.chmodSync(binaryPath, '755');
    safeLog('Made binary executable');
  } catch (error) {
    safeLog('Could not make binary executable: ' + error.message, 'error');
  }

  // Set up environment variables for the backend
  const backendEnv = {
    ...process.env,
    KASPER_Q_DATA_DIR: path.join(require('os').homedir(), 'Library/Application Support/Kasper-Q'),
    KASPER_Q_CONFIG_DIR: path.join(require('os').homedir(), 'Library/Preferences'),
    KASPER_Q_CACHE_DIR: path.join(require('os').homedir(), 'Library/Caches/Kasper-Q')
  };

  // Create necessary directories
  try {
    fs.mkdirSync(backendEnv.KASPER_Q_DATA_DIR, { recursive: true });
    fs.mkdirSync(backendEnv.KASPER_Q_CACHE_DIR, { recursive: true });
  } catch (error) {
    // console.log('Could not create backend directories:', error.message);
  }

  // Set working directory based on backend type
  let workingDir;
  if (binaryPath.includes('backend/kasper_q_menubar')) {
    // For embedded backend, use the directory containing the binary (maintains PyInstaller structure)
    workingDir = path.dirname(binaryPath);
  } else {
    // For other backends, use the directory containing the binary
    workingDir = path.dirname(binaryPath);
  }

  // console.log('Working directory:', workingDir);

  // Run the binary directly
  // console.log('Spawning backend process...');
  backendProcess = spawn(binaryPath, [], {
    stdio: ['pipe', 'pipe', 'pipe'],
    env: backendEnv,
    cwd: workingDir,
    // Create a new process group for better process management
    detached: false
  });

  // Add error handling for the spawn process
  backendProcess.on('error', (error) => {
    safeLog('Failed to start backend process: ' + error.message, 'error');
  });

  backendProcess.on('exit', (code, signal) => {
    safeLog(`Backend process exited with code ${code} and signal ${signal}`);
  });

  backendProcess.stdout.on('data', (data) => {
    safeLog('Backend stdout: ' + data.toString());
  });

  backendProcess.stderr.on('data', (data) => {
    safeLog('Backend stderr: ' + data.toString(), 'error');
  });

  safeLog('Backend process spawned with PID: ' + backendProcess.pid);

  backendProcess.stdout.on('data', () => {
    // Output handling disabled for performance
  });

  backendProcess.stderr.on('data', () => {
    // Error output handling disabled for performance
  });

  backendProcess.on('close', () => {
    // Process cleanup
    backendProcess = null;
  });

  backendProcess.on('error', () => {
    // Error handling disabled for performance
    backendProcess = null;
  });

  // Store the process ID for better cleanup
  if (backendProcess.pid) {
    // console.log('Backend process started with PID:', backendProcess.pid);
  }
}

/**
 * Checks if the backend server is running
 */
function isBackendServerRunning() {
  return backendProcess && !backendProcess.killed;
}

/**
 * Gets the backend process status
 */
function getBackendServerStatus() {
  if (!backendProcess) {
    return { running: false, pid: null };
  }
  
  return {
    running: !backendProcess.killed,
    pid: backendProcess.pid,
    exitCode: backendProcess.exitCode
  };
}

/**
 * Stops the backend server gracefully
 */
async function stopBackendServer() {
  if (!backendProcess) {
    // safeLog('No backend process to stop');
    return;
  }

  // safeLog('Stopping backend server...');

  try {
    // First, try to gracefully terminate the process
    if (backendProcess.pid) {
      // safeLog('Sending SIGTERM to backend process PID: ' + backendProcess.pid);
      
      // Try to kill the process group to ensure all child processes are terminated
      try {
        process.kill(-backendProcess.pid, 'SIGTERM');
      } catch (groupError) {
        // safeLog('Could not kill process group, trying direct kill: ' + groupError.message);
        // Fallback to direct process kill
        backendProcess.kill('SIGTERM');
      }
    } else {
      // Fallback to regular kill if no PID
      backendProcess.kill('SIGTERM');
    }
  } catch (error) {
    // safeLog('Error sending SIGTERM: ' + error.message);
    // Try force kill as fallback
    try {
      backendProcess.kill('SIGKILL');
    } catch (killError) {
      // safeLog('Error force killing: ' + killError.message);
    }
  }

  // Wait for graceful shutdown (10 seconds for binary processes)
  const timeout = setTimeout(() => {
    if (backendProcess) {
      // safeLog('Force killing backend process after timeout...');
      try {
        if (backendProcess.pid) {
          // Try to kill the entire process tree
          try {
            process.kill(-backendProcess.pid, 'SIGKILL');
          } catch (groupError) {
            backendProcess.kill('SIGKILL');
          }
        } else {
          backendProcess.kill('SIGKILL');
        }
      } catch (error) {
        // safeLog('Error force killing: ' + error.message);
      }
    }
  }, 10000);

  return new Promise((resolve) => {
    if (backendProcess) {
      backendProcess.on('close', () => {
        clearTimeout(timeout);
        // safeLog('Backend server stopped gracefully');
        backendProcess = null;
        resolve();
      });
      
      backendProcess.on('exit', () => {
        clearTimeout(timeout);
        // safeLog('Backend server exited');
        backendProcess = null;
        resolve();
      });
    } else {
      clearTimeout(timeout);
      resolve();
    }
  });
}

/** #7C4DFF - #7C4DFF
 * Creates the main application window with proper sizing and configuration
 */ 
function createWindow() {
  // Create browser window with optimized settings for minimal resource usage
  mainWindow = new BrowserWindow({
    width: 1000,
    height: 600,
    resizable: false,
    maximizable: false,
    fullscreenable: false,
    backgroundColor: '#09090b', // Dark background matching our theme
    show: false, // Don't show until ready-to-show
    frame: false, // Custom frame for our cyberpunk UI
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      // Performance optimizations
      backgroundThrottling: false, // Prevent throttling when window is hidden
      offscreen: false, // Disable offscreen rendering for better performance
      spellcheck: false, // Disable spellcheck to save memory
      enableWebSQL: false, // Disable WebSQL for security and performance
      webSecurity: true, // Keep security enabled
      allowRunningInsecureContent: false,
      experimentalFeatures: false, // Disable experimental features
      // Memory optimizations
      v8CacheOptions: 'code', // Enable V8 code caching
      enableBlinkFeatures: '', // Disable unnecessary Blink features
      disableBlinkFeatures: 'Auxclick' // Disable unnecessary features
    }
  });

  // Load the React app
  // console.log('isDev value:', isDev);
  // console.log('Current working directory:', process.cwd());
  
  // Check if we're in development mode
  const isDevelopmentMode = process.env.NODE_ENV === 'development';
  const isPackaged = !isDev; // Use electron-is-dev to detect if we're in a packaged app
  const buildPath = path.join(__dirname, '../build');
  const hasBuildDir = fs.existsSync(buildPath);
  // console.log('Development mode:', isDevelopmentMode);
  // console.log('Is packaged:', isPackaged);
  // console.log('Has build directory:', hasBuildDir);

  // Only use dev server if we're truly in development (not packaged) and have build dir
  const startUrl = (isDevelopmentMode && !isPackaged && hasBuildDir)
    ? 'http://localhost:3000'
    : 'http://localhost:1109';
  
  // console.log('Loading URL:', startUrl);
  mainWindow.loadURL(startUrl);

  // Show window when ready and centered
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    mainWindow.center();
    startupComplete = true; // Mark startup as complete
    // safeLog('Application startup completed successfully');
  });

  // Handle window controls (custom frame)
  ipcMain.on('window-minimize', () => {
    mainWindow.minimize();
  });

  ipcMain.on('window-maximize', () => {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  });

  ipcMain.on('window-close', async () => {
    // safeLog('Window close requested, shutting down services...');
    await stopBackendServer();
    stopHttpServer();
    stopReactDevServer();
    mainWindow.close();
  });

  // Handle application shutdown
  ipcMain.handle('shutdown-application', async () => {
    // safeLog('Application shutdown requested...');
    await stopBackendServer();
    stopHttpServer();
    stopReactDevServer();
    app.quit();
  });

  // Open DevTools in development mode
  if (isDev) {
    mainWindow.webContents.openDevTools({ mode: 'detach' });
  }
}

// Create window when Electron is ready
app.whenReady().then(async () => {
  // console.log('Electron app is ready, starting services...');
  
  // Check if we're in development mode
  const isDevelopmentMode = process.env.NODE_ENV === 'development';
  const isPackaged = !isDev; // Use electron-is-dev to detect if we're in a packaged app
  const buildPath = path.join(__dirname, '../build');
  const hasBuildDir = fs.existsSync(buildPath);

  // console.log('Environment check:');
  // console.log('- NODE_ENV:', process.env.NODE_ENV);
  // console.log('- isDev (electron-is-dev):', isDev);
  // console.log('- isPackaged:', isPackaged);
  // console.log('- hasBuildDir:', hasBuildDir);

  if (isDevelopmentMode && !isPackaged && hasBuildDir) {
    // True development mode - start React dev server
    // console.log('True development mode detected, starting React dev server...');
    reactDevProcess = startReactDevServer();

    // Wait for React dev server to be ready
    // console.log('Waiting for React dev server to be ready...');
    await new Promise(resolve => setTimeout(resolve, 8000));
  } else {
    // Production mode or packaged app - serve built files
    // console.log('Production/packaged mode detected, starting HTTP server...');
    startHttpServer();
    
    // Wait a moment for HTTP server to start
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // Start the backend server
  // console.log('Starting backend server...');
  startBackendServer();
  
  // Wait a moment for backend to start
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // Then create the window
  // console.log('Creating main window...');
  createWindow();
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', async () => {
  if (process.platform !== 'darwin') {
    try {
      // safeLog('All windows closed, stopping services...');
      await stopBackendServer();
      stopHttpServer();
      stopReactDevServer();

      // Small delay before quit
      setTimeout(() => {
        app.quit();
      }, 100);
    } catch (error) {
      // safeLog('Error during window-all-closed cleanup: ' + error.message, 'error');
      app.quit();
    }
  }
});

// On macOS, recreate window when dock icon is clicked
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Handle app quit to ensure all services are stopped
app.on('before-quit', async (event) => {
  event.preventDefault();

  try {
    // safeLog('Application quit requested, stopping all services...');

    // Stop services in order with proper error handling
    await stopBackendServer();
    stopHttpServer();
    stopReactDevServer();

    // Small delay to ensure cleanup completes before exit
    setTimeout(() => {
      app.exit();
    }, 100);

  } catch (error) {
    // safeLog('Error during shutdown: ' + error.message, 'error');
    // Force exit if cleanup fails
    setTimeout(() => {
      app.exit();
    }, 500);
  }
});

ipcMain.handle('open-external-url', async (_, url) => {
  // console.log('Main: 🎯 open-external-url handler fired for', url)
  try {
    // <-- HERE is where you pass { activate: true }
    await shell.openExternal(url, { activate: true })
    // console.log('Main: ✅ shell.openExternal succeeded')
    return true
  } catch (err) {
    // console.error('Main: ❌ shell.openExternal failed:', err)
    throw err
  }
})

// Backend server management IPC handlers
ipcMain.handle('get-backend-status', async () => {
  return getBackendServerStatus();
});

ipcMain.handle('restart-backend', async () => {
  // safeLog('Restarting backend server...');
  await stopBackendServer();
  // Wait a moment before restarting
  await new Promise(resolve => setTimeout(resolve, 1000));
  startBackendServer();
  return getBackendServerStatus();
});